// Setup heroes database tables and data
const mysql = require('mysql2/promise');
const fs = require('fs');

async function setupHeroesDatabase() {
    let connection;
    
    try {
        console.log('🔥 Connecting to database...');
        
        connection = await mysql.createConnection({
            host: 'localhost',
            user: 'root',
            password: 'PolitieDB2025SecurePassword',
            database: 'whiteout_dashboard',
            multipleStatements: true
        });
        
        console.log('✅ Connected to database');
        
        // Read and execute the heroes setup script
        const setupScript = fs.readFileSync('heroes-database-setup.sql', 'utf8');
        
        console.log('🔥 Executing heroes database setup...');
        
        // Split script into individual statements
        const statements = setupScript.split(';').filter(stmt => stmt.trim().length > 0);
        
        for (const statement of statements) {
            if (statement.trim()) {
                try {
                    await connection.execute(statement.trim());
                    console.log('✅ Executed:', statement.substring(0, 50) + '...');
                } catch (error) {
                    if (error.code === 'ER_TABLE_EXISTS_ERROR' || error.code === 'ER_DUP_ENTRY') {
                        console.log('ℹ️  Already exists:', statement.substring(0, 50) + '...');
                    } else {
                        console.error('❌ Error executing statement:', statement.substring(0, 50) + '...');
                        console.error('Error:', error.message);
                    }
                }
            }
        }
        
        console.log('🔥 Heroes database setup completed!');
        
        // Verify tables were created
        const [tables] = await connection.execute("SHOW TABLES LIKE 'heroes%'");
        console.log('📋 Heroes tables:', tables.map(t => Object.values(t)[0]));
        
        // Check heroes data
        const [heroes] = await connection.execute('SELECT name, generation, type FROM heroes ORDER BY generation, name');
        console.log('🦸 Heroes in database:', heroes.length);
        heroes.forEach(hero => {
            console.log(`  - ${hero.name} (Gen ${hero.generation}, ${hero.type})`);
        });
        
    } catch (error) {
        console.error('❌ Heroes database setup failed:', error);
        process.exit(1);
    } finally {
        if (connection) {
            await connection.end();
            console.log('🔥 Database connection closed');
        }
    }
}

// Run the setup
setupHeroesDatabase().then(() => {
    console.log('🎉 Heroes database setup complete!');
    process.exit(0);
}).catch(error => {
    console.error('💥 Setup failed:', error);
    process.exit(1);
});
