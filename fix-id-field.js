// Fix id field to be proper AUTO_INCREMENT
const mysql = require('mysql2/promise');

async function fixIdField() {
    let connection;
    
    try {
        console.log('🔥 Connecting to database...');
        
        connection = await mysql.createConnection({
            host: 'localhost',
            user: 'root',
            password: 'PolitieDB2025SecurePassword',
            database: 'whiteout_dashboard'
        });
        
        console.log('✅ Connected to database');
        
        // Clear all data first
        console.log('🔥 Clearing heroes data...');
        await connection.execute('DELETE FROM heroes');
        
        // Drop primary key constraint
        console.log('🔥 Dropping primary key...');
        try {
            await connection.execute('ALTER TABLE heroes DROP PRIMARY KEY');
            console.log('✅ Dropped primary key');
        } catch (error) {
            console.log('ℹ️  Primary key drop:', error.message);
        }
        
        // Modify id column to be INT AUTO_INCREMENT
        console.log('🔥 Modifying id column...');
        await connection.execute('ALTER TABLE heroes MODIFY COLUMN id INT AUTO_INCREMENT');
        
        // Add primary key back
        console.log('🔥 Adding primary key back...');
        await connection.execute('ALTER TABLE heroes ADD PRIMARY KEY (id)');
        
        console.log('✅ ID field fixed');
        
        // Verify structure
        const [columns] = await connection.execute('DESCRIBE heroes');
        const idColumn = columns.find(col => col.Field === 'id');
        console.log('📋 ID column:', idColumn);
        
    } catch (error) {
        console.error('❌ Error fixing id field:', error);
        process.exit(1);
    } finally {
        if (connection) {
            await connection.end();
            console.log('🔥 Database connection closed');
        }
    }
}

// Run the fix
fixIdField().then(() => {
    console.log('🎉 ID field fix complete!');
    process.exit(0);
}).catch(error => {
    console.error('💥 Fix failed:', error);
    process.exit(1);
});
