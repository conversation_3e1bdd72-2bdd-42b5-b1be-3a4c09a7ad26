// Add remaining heroes (Gen 9-11, Rare, Epic)
const mysql = require('mysql2/promise');

async function addRemainingHeroes() {
    let connection;
    
    try {
        console.log('🔥 Connecting to database...');
        
        connection = await mysql.createConnection({
            host: 'localhost',
            user: 'root',
            password: 'PolitieDB2025SecurePassword',
            database: 'whiteout_dashboard'
        });
        
        console.log('✅ Connected to database');
        
        // Add remaining heroes data
        const remainingHeroes = [
            // Generation 9
            ['Bahiti', '9', 'infantry', 'images/heroes/bahiti_gen9.png', 'Enhanced desert warrior with advanced sand magic', JSON.stringify(['Greater Desert Storm', 'Sandstorm Shield', 'Dune Master']), JSON.stringify({attack: 1450, defense: 1180, health: 15500}), 25, 'common'],
            ['<PERSON>', '9', 'marksman', 'images/heroes/flint_gen9.png', 'Master marksman with legendary precision', JSON.stringify(['Perfect Shot', '<PERSON>\'s Vision', 'Explosive Mastery']), JSON.stringify({attack: 1650, defense: 700, health: 10200}), 26, 'common'],
            ['<PERSON>', '9', 'lancer', 'images/heroes/natalia_gen9.png', 'Elite cavalry commander with war experience', JSON.stringify(['War Charge', 'Battle Commander', 'Victory Lance']), JSON.stringify({attack: 1420, defense: 1040, health: 13500}), 27, 'common'],
            
            // Generation 10
            ['Molly', '10', 'infantry', 'images/heroes/molly_gen10.png', 'Ultimate defensive specialist with fortress abilities', JSON.stringify(['Fortress Wall', 'Unbreakable Defense', 'Guardian\'s Resolve']), JSON.stringify({attack: 1500, defense: 1250, health: 16000}), 28, 'common'],
            ['Patrick', '10', 'marksman', 'images/heroes/patrick_gen10.png', 'Shadow master with invisible strikes', JSON.stringify(['Shadow Shot', 'Phantom Strike', 'Assassin\'s Mark']), JSON.stringify({attack: 1700, defense: 720, health: 10500}), 29, 'common'],
            ['Wayne', '10', 'lancer', 'images/heroes/wayne_gen10.png', 'Legendary mounted warrior with divine lance', JSON.stringify(['Divine Lance', 'Celestial Charge', 'Holy Strike']), JSON.stringify({attack: 1480, defense: 1080, health: 14000}), 30, 'common'],
            
            // Generation 11
            ['Jessie', '11', 'infantry', 'images/heroes/jessie_gen11.png', 'Apex berserker with unstoppable rage', JSON.stringify(['Unstoppable Rage', 'Berserker\'s Fury', 'Rampage']), JSON.stringify({attack: 1550, defense: 1300, health: 16500}), 31, 'common'],
            ['Zoe', '11', 'marksman', 'images/heroes/zoe_gen11.png', 'Master hunter with multi-dimensional arrows', JSON.stringify(['Dimensional Shot', 'Hunter\'s Mastery', 'Arrow Storm']), JSON.stringify({attack: 1750, defense: 740, health: 11000}), 32, 'common'],
            ['Gina', '11', 'lancer', 'images/heroes/gina_gen11.png', 'Spear saint with transcendent combat skills', JSON.stringify(['Transcendent Spear', 'Combat Saint', 'Ultimate Strike']), JSON.stringify({attack: 1520, defense: 1120, health: 14500}), 33, 'common'],
            
            // Rare Heroes
            ['Frost Guardian', 'rare', 'infantry', 'images/heroes/frost_guardian.png', 'Legendary ice warrior with eternal frost powers', JSON.stringify(['Eternal Frost', 'Ice Fortress', 'Glacial Dominion']), JSON.stringify({attack: 1800, defense: 1500, health: 18000}), 34, 'rare'],
            ['Ice Sentinel', 'rare', 'infantry', 'images/heroes/ice_sentinel.png', 'Ancient guardian of the frozen realm', JSON.stringify(['Frozen Realm', 'Ice Prison', 'Arctic Shield']), JSON.stringify({attack: 1750, defense: 1600, health: 19000}), 35, 'rare'],
            ['Blizzard Archer', 'rare', 'marksman', 'images/heroes/blizzard_archer.png', 'Master of ice arrows and winter storms', JSON.stringify(['Blizzard Arrow', 'Winter Storm', 'Absolute Zero']), JSON.stringify({attack: 2000, defense: 800, health: 12000}), 36, 'rare'],
            ['Frost Sniper', 'rare', 'marksman', 'images/heroes/frost_sniper.png', 'Elite marksman with crystalline precision', JSON.stringify(['Crystal Shot', 'Ice Shard', 'Frozen Precision']), JSON.stringify({attack: 2100, defense: 750, health: 11500}), 37, 'rare'],
            ['Glacier Lance', 'rare', 'lancer', 'images/heroes/glacier_lance.png', 'Wielder of the legendary ice lance', JSON.stringify(['Glacier Strike', 'Ice Lance Mastery', 'Frozen Charge']), JSON.stringify({attack: 1900, defense: 1200, health: 15000}), 38, 'rare'],
            ['Winter Knight', 'rare', 'lancer', 'images/heroes/winter_knight.png', 'Noble knight of the eternal winter', JSON.stringify(['Winter\'s Edge', 'Chivalrous Strike', 'Frost Armor']), JSON.stringify({attack: 1850, defense: 1300, health: 16000}), 39, 'rare'],
            
            // Epic Heroes
            ['Inferno Warlord', 'epic', 'infantry', 'images/heroes/inferno_warlord.png', 'Legendary fire lord with apocalyptic power', JSON.stringify(['Inferno Apocalypse', 'Flame Emperor', 'Hellfire Domain']), JSON.stringify({attack: 2500, defense: 2000, health: 25000}), 40, 'epic'],
            ['Void Destroyer', 'epic', 'infantry', 'images/heroes/void_destroyer.png', 'Destroyer from the void realm', JSON.stringify(['Void Annihilation', 'Reality Tear', 'Chaos Strike']), JSON.stringify({attack: 2600, defense: 1900, health: 24000}), 41, 'epic'],
            ['Storm Empress', 'epic', 'marksman', 'images/heroes/storm_empress.png', 'Empress of lightning and thunder', JSON.stringify(['Lightning Storm', 'Thunder Strike', 'Storm Dominion']), JSON.stringify({attack: 2800, defense: 1000, health: 15000}), 42, 'epic'],
            ['Shadow Reaper', 'epic', 'marksman', 'images/heroes/shadow_reaper.png', 'Death incarnate with shadow arrows', JSON.stringify(['Death Arrow', 'Shadow Realm', 'Soul Harvest']), JSON.stringify({attack: 2900, defense: 900, health: 14000}), 43, 'epic'],
            ['Dragon Slayer', 'epic', 'lancer', 'images/heroes/dragon_slayer.png', 'Legendary hero who slayed the ancient dragon', JSON.stringify(['Dragon Bane', 'Legendary Strike', 'Hero\'s Resolve']), JSON.stringify({attack: 2700, defense: 1500, health: 20000}), 44, 'epic'],
            ['Celestial Spear', 'epic', 'lancer', 'images/heroes/celestial_spear.png', 'Divine warrior with heavenly spear', JSON.stringify(['Celestial Strike', 'Divine Judgment', 'Heaven\'s Wrath']), JSON.stringify({attack: 2650, defense: 1600, health: 21000}), 45, 'epic']
        ];
        
        console.log('🔥 Inserting remaining heroes...');
        
        for (const hero of remainingHeroes) {
            await connection.execute(
                'INSERT INTO heroes (name, generation, type, image_url, description, skills, stats, tier_ranking, rarity) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
                hero
            );
        }
        
        console.log(`✅ Inserted ${remainingHeroes.length} additional heroes`);
        
        // Verify heroes by generation
        const [heroes] = await connection.execute(`
            SELECT generation, COUNT(*) as count 
            FROM heroes 
            GROUP BY generation 
            ORDER BY FIELD(generation, '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', 'rare', 'epic')
        `);
        
        console.log('🦸 Heroes by generation:');
        heroes.forEach(hero => {
            console.log(`  Gen ${hero.generation}: ${hero.count} heroes`);
        });
        
        // Show total count
        const [total] = await connection.execute('SELECT COUNT(*) as total FROM heroes');
        console.log(`📊 Total heroes: ${total[0].total}`);
        
    } catch (error) {
        console.error('❌ Error adding remaining heroes:', error);
        process.exit(1);
    } finally {
        if (connection) {
            await connection.end();
            console.log('🔥 Database connection closed');
        }
    }
}

// Run the addition
addRemainingHeroes().then(() => {
    console.log('🎉 Remaining heroes addition complete!');
    process.exit(0);
}).catch(error => {
    console.error('💥 Addition failed:', error);
    process.exit(1);
});
