// Check and fix table structure
const mysql = require('mysql2/promise');

async function checkTableStructure() {
    let connection;
    
    try {
        console.log('🔥 Connecting to database...');
        
        connection = await mysql.createConnection({
            host: 'localhost',
            user: 'root',
            password: 'PolitieDB2025SecurePassword',
            database: 'whiteout_dashboard'
        });
        
        console.log('✅ Connected to database');
        
        // Check current table structure
        const [columns] = await connection.execute('DESCRIBE heroes');
        console.log('📋 Current table structure:');
        columns.forEach(col => {
            console.log(`  ${col.Field}: ${col.Type} ${col.Null} ${col.Key} ${col.Default} ${col.Extra}`);
        });
        
        // Drop and recreate table with correct structure
        console.log('🔥 Recreating heroes table...');
        
        await connection.execute('DROP TABLE IF EXISTS heroes');
        
        await connection.execute(`
            CREATE TABLE heroes (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                generation ENUM('1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', 'rare', 'epic') NOT NULL,
                type ENUM('infantry', 'marksman', 'lancer') NOT NULL,
                image_url VARCHAR(255),
                description TEXT,
                skills JSON,
                stats JSON,
                tier_ranking INT DEFAULT 0,
                rarity ENUM('common', 'rare', 'epic', 'legendary') DEFAULT 'common',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_by INT,
                INDEX idx_generation (generation),
                INDEX idx_type (type),
                INDEX idx_rarity (rarity),
                INDEX idx_tier_ranking (tier_ranking)
            )
        `);
        
        console.log('✅ Heroes table recreated');
        
        // Verify new structure
        const [newColumns] = await connection.execute('DESCRIBE heroes');
        console.log('📋 New table structure:');
        newColumns.forEach(col => {
            console.log(`  ${col.Field}: ${col.Type} ${col.Null} ${col.Key} ${col.Default} ${col.Extra}`);
        });
        
    } catch (error) {
        console.error('❌ Error checking table structure:', error);
        process.exit(1);
    } finally {
        if (connection) {
            await connection.end();
            console.log('🔥 Database connection closed');
        }
    }
}

// Run the check
checkTableStructure().then(() => {
    console.log('🎉 Table structure check complete!');
    process.exit(0);
}).catch(error => {
    console.error('💥 Check failed:', error);
    process.exit(1);
});
