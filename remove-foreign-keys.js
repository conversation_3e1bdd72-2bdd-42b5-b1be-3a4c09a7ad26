// Remove foreign key constraints and fix heroes table
const mysql = require('mysql2/promise');

async function removeForeignKeys() {
    let connection;
    
    try {
        console.log('🔥 Connecting to database...');
        
        connection = await mysql.createConnection({
            host: 'localhost',
            user: 'root',
            password: 'PolitieDB2025SecurePassword',
            database: 'whiteout_dashboard'
        });
        
        console.log('✅ Connected to database');
        
        // Check for foreign key constraints
        console.log('🔍 Checking foreign key constraints...');
        const [constraints] = await connection.execute(`
            SELECT 
                CONSTRAINT_NAME,
                TABLE_NAME,
                COLUMN_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM 
                INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
            WHERE 
                REFERENCED_TABLE_SCHEMA = 'whiteout_dashboard' 
                AND REFERENCED_TABLE_NAME = 'heroes'
        `);
        
        console.log('📋 Foreign key constraints found:');
        constraints.forEach(constraint => {
            console.log(`  ${constraint.TABLE_NAME}.${constraint.CONSTRAINT_NAME} -> heroes.${constraint.REFERENCED_COLUMN_NAME}`);
        });
        
        // Drop foreign key constraints
        for (const constraint of constraints) {
            try {
                console.log(`🔥 Dropping constraint ${constraint.CONSTRAINT_NAME} from ${constraint.TABLE_NAME}...`);
                await connection.execute(`ALTER TABLE ${constraint.TABLE_NAME} DROP FOREIGN KEY ${constraint.CONSTRAINT_NAME}`);
                console.log(`✅ Dropped ${constraint.CONSTRAINT_NAME}`);
            } catch (error) {
                console.log(`❌ Error dropping ${constraint.CONSTRAINT_NAME}:`, error.message);
            }
        }
        
        // Drop any related tables that might be causing issues
        const tablesToDrop = ['hero_skills', 'hero_uploads'];
        for (const table of tablesToDrop) {
            try {
                console.log(`🔥 Dropping table ${table}...`);
                await connection.execute(`DROP TABLE IF EXISTS ${table}`);
                console.log(`✅ Dropped ${table}`);
            } catch (error) {
                console.log(`❌ Error dropping ${table}:`, error.message);
            }
        }
        
        // Clear heroes data
        console.log('🔥 Clearing heroes data...');
        await connection.execute('DELETE FROM heroes');
        
        // Drop primary key
        console.log('🔥 Dropping primary key...');
        try {
            await connection.execute('ALTER TABLE heroes DROP PRIMARY KEY');
            console.log('✅ Dropped primary key');
        } catch (error) {
            console.log('ℹ️  Primary key drop:', error.message);
        }
        
        // Modify id column
        console.log('🔥 Modifying id column...');
        await connection.execute('ALTER TABLE heroes MODIFY COLUMN id INT AUTO_INCREMENT');
        
        // Add primary key back
        console.log('🔥 Adding primary key back...');
        await connection.execute('ALTER TABLE heroes ADD PRIMARY KEY (id)');
        
        console.log('✅ Heroes table fixed');
        
        // Verify structure
        const [columns] = await connection.execute('DESCRIBE heroes');
        const idColumn = columns.find(col => col.Field === 'id');
        console.log('📋 ID column:', idColumn);
        
    } catch (error) {
        console.error('❌ Error removing foreign keys:', error);
        process.exit(1);
    } finally {
        if (connection) {
            await connection.end();
            console.log('🔥 Database connection closed');
        }
    }
}

// Run the removal
removeForeignKeys().then(() => {
    console.log('🎉 Foreign keys removal complete!');
    process.exit(0);
}).catch(error => {
    console.error('💥 Removal failed:', error);
    process.exit(1);
});
