// Final fix for heroes table
const mysql = require('mysql2/promise');

async function finalFixHeroes() {
    let connection;
    
    try {
        console.log('🔥 Connecting to database...');
        
        connection = await mysql.createConnection({
            host: 'localhost',
            user: 'root',
            password: 'PolitieDB2025SecurePassword',
            database: 'whiteout_dashboard'
        });
        
        console.log('✅ Connected to database');
        
        // Clear heroes data
        console.log('🔥 Clearing heroes data...');
        await connection.execute('DELETE FROM heroes');
        
        // Modify id column to be INT AUTO_INCREMENT PRIMARY KEY in one go
        console.log('🔥 Modifying id column to be AUTO_INCREMENT PRIMARY KEY...');
        await connection.execute('ALTER TABLE heroes MODIFY COLUMN id INT AUTO_INCREMENT PRIMARY KEY');
        
        console.log('✅ Heroes table fixed');
        
        // Verify structure
        const [columns] = await connection.execute('DESCRIBE heroes');
        const idColumn = columns.find(col => col.Field === 'id');
        console.log('📋 ID column:', idColumn);
        
        // Now insert heroes data
        console.log('🔥 Inserting heroes data...');
        
        const heroesData = [
            // Generation 1
            ['Bahiti', '1', 'infantry', 'images/heroes/bahiti.png', 'Desert warrior with sand-based abilities', JSON.stringify(['Desert Storm', 'Sand Shield', 'Mirage Strike']), JSON.stringify({attack: 850, defense: 920, health: 12500}), 1, 'common'],
            ['Flint', '1', 'marksman', 'images/heroes/flint.png', 'Expert marksman with precision abilities', JSON.stringify(['Precision Shot', 'Eagle Eye', 'Explosive Arrow']), JSON.stringify({attack: 1200, defense: 650, health: 8500}), 2, 'common'],
            ['Natalia', '1', 'lancer', 'images/heroes/natalia.png', 'Cavalry commander with mounted combat skills', JSON.stringify(['Cavalry Charge', 'Battle Fury', 'Lance Strike']), JSON.stringify({attack: 980, defense: 780, health: 10200}), 3, 'common'],
            
            // Generation 2
            ['Molly', '2', 'infantry', 'images/heroes/molly.png', 'Defensive specialist with shield abilities', JSON.stringify(['Shield Wall', 'Defensive Stance', 'Counter Attack']), JSON.stringify({attack: 720, defense: 1100, health: 14000}), 4, 'common'],
            ['Patrick', '2', 'marksman', 'images/heroes/patrick.png', 'Stealth marksman with sniper abilities', JSON.stringify(['Sniper Shot', 'Stealth', 'Critical Strike']), JSON.stringify({attack: 1350, defense: 580, health: 7800}), 5, 'common'],
            ['Wayne', '2', 'lancer', 'images/heroes/wayne.png', 'Mounted warrior with lance combat expertise', JSON.stringify(['Lance Strike', 'Mounted Combat', 'Charge Attack']), JSON.stringify({attack: 1050, defense: 720, health: 9800}), 6, 'common'],
            
            // Generation 3
            ['Jessie', '3', 'infantry', 'images/heroes/jessie.png', 'Berserker with rage-based abilities', JSON.stringify(['Berserker Rage', 'Iron Will', 'Fury Strike']), JSON.stringify({attack: 1100, defense: 850, health: 11500}), 7, 'common'],
            ['Zoe', '3', 'marksman', 'images/heroes/zoe.png', 'Multi-shot specialist with hunter abilities', JSON.stringify(['Multi-Shot', 'Hunter\'s Mark', 'Piercing Arrow']), JSON.stringify({attack: 1280, defense: 620, health: 8200}), 8, 'common'],
            ['Gina', '3', 'lancer', 'images/heroes/gina.png', 'Combat master with spear expertise', JSON.stringify(['Spear Thrust', 'Combat Mastery', 'Whirlwind Attack']), JSON.stringify({attack: 1150, defense: 800, health: 10500}), 9, 'common'],
            
            // Generation 4
            ['Reginald', '4', 'infantry', 'images/heroes/reginald.png', 'Royal guard with noble combat skills', JSON.stringify(['Royal Guard', 'Noble Strike', 'Honor Shield']), JSON.stringify({attack: 1200, defense: 950, health: 13000}), 10, 'common'],
            ['Cloris', '4', 'marksman', 'images/heroes/cloris.png', 'Elven archer with nature magic', JSON.stringify(['Nature\'s Arrow', 'Forest Blessing', 'Wind Shot']), JSON.stringify({attack: 1400, defense: 600, health: 8800}), 11, 'common'],
            ['Alonso', '4', 'lancer', 'images/heroes/alonso.png', 'Knight with heavy armor and lance', JSON.stringify(['Heavy Charge', 'Armor Break', 'Knight\'s Honor']), JSON.stringify({attack: 1180, defense: 850, health: 11200}), 12, 'common'],
            
            // Generation 5
            ['Sergey', '5', 'infantry', 'images/heroes/sergey.png', 'Russian warrior with ice abilities', JSON.stringify(['Ice Strike', 'Frozen Shield', 'Winter\'s Wrath']), JSON.stringify({attack: 1250, defense: 980, health: 13500}), 13, 'common'],
            ['Hilda', '5', 'marksman', 'images/heroes/hilda.png', 'Viking archer with frost arrows', JSON.stringify(['Frost Arrow', 'Ice Storm', 'Nordic Aim']), JSON.stringify({attack: 1450, defense: 620, health: 9000}), 14, 'common'],
            ['Brooke', '5', 'lancer', 'images/heroes/brooke.png', 'Ice lancer with freezing attacks', JSON.stringify(['Freeze Lance', 'Ice Barrier', 'Glacial Charge']), JSON.stringify({attack: 1220, defense: 880, health: 11800}), 15, 'common'],
            
            // Generation 6
            ['Jeronimo', '6', 'infantry', 'images/heroes/jeronimo.png', 'Apache warrior with tribal magic', JSON.stringify(['Tribal War Cry', 'Spirit Shield', 'Ancestral Power']), JSON.stringify({attack: 1300, defense: 1020, health: 14000}), 16, 'common'],
            ['Zinman', '6', 'marksman', 'images/heroes/zinman.png', 'Sharpshooter with explosive rounds', JSON.stringify(['Explosive Shot', 'Rapid Fire', 'Demolition']), JSON.stringify({attack: 1500, defense: 640, health: 9200}), 17, 'common'],
            ['Miho', '6', 'lancer', 'images/heroes/miho.png', 'Samurai with katana mastery', JSON.stringify(['Katana Slash', 'Bushido Spirit', 'Honor Strike']), JSON.stringify({attack: 1280, defense: 920, health: 12200}), 18, 'common'],
            
            // Generation 7
            ['Rusty', '7', 'infantry', 'images/heroes/rusty.png', 'Mechanic with robotic enhancements', JSON.stringify(['Mech Suit', 'Repair Protocol', 'Steel Fist']), JSON.stringify({attack: 1350, defense: 1080, health: 14500}), 19, 'common'],
            ['Maddie', '7', 'marksman', 'images/heroes/maddie.png', 'Tech sniper with laser weapons', JSON.stringify(['Laser Sight', 'Energy Blast', 'Tech Overload']), JSON.stringify({attack: 1550, defense: 660, health: 9500}), 20, 'common'],
            ['Kelvins', '7', 'lancer', 'images/heroes/kelvins.png', 'Cyber knight with plasma lance', JSON.stringify(['Plasma Lance', 'Cyber Shield', 'Digital Strike']), JSON.stringify({attack: 1320, defense: 960, health: 12600}), 21, 'common'],
            
            // Generation 8
            ['Yuki', '8', 'infantry', 'images/heroes/yuki.png', 'Snow ninja with stealth abilities', JSON.stringify(['Shadow Clone', 'Ice Shuriken', 'Ninja Vanish']), JSON.stringify({attack: 1400, defense: 1120, health: 15000}), 22, 'common'],
            ['Tracey', '8', 'marksman', 'images/heroes/tracey.png', 'Elite sniper with thermal vision', JSON.stringify(['Thermal Scope', 'Piercing Shot', 'Headhunter']), JSON.stringify({attack: 1600, defense: 680, health: 9800}), 23, 'common'],
            ['Jasser', '8', 'lancer', 'images/heroes/jasser.png', 'Desert warrior with scimitar', JSON.stringify(['Scimitar Dance', 'Desert Wind', 'Mirage Strike']), JSON.stringify({attack: 1380, defense: 1000, health: 13000}), 24, 'common']
        ];
        
        for (const hero of heroesData) {
            await connection.execute(
                'INSERT INTO heroes (name, generation, type, image_url, description, skills, stats, tier_ranking, rarity) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
                hero
            );
        }
        
        console.log(`✅ Inserted ${heroesData.length} heroes`);
        
        // Verify heroes by generation
        const [heroes] = await connection.execute(`
            SELECT generation, COUNT(*) as count 
            FROM heroes 
            GROUP BY generation 
            ORDER BY FIELD(generation, '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', 'rare', 'epic')
        `);
        
        console.log('🦸 Heroes by generation:');
        heroes.forEach(hero => {
            console.log(`  Gen ${hero.generation}: ${hero.count} heroes`);
        });
        
        // Show total count
        const [total] = await connection.execute('SELECT COUNT(*) as total FROM heroes');
        console.log(`📊 Total heroes: ${total[0].total}`);
        
    } catch (error) {
        console.error('❌ Error in final fix:', error);
        process.exit(1);
    } finally {
        if (connection) {
            await connection.end();
            console.log('🔥 Database connection closed');
        }
    }
}

// Run the final fix
finalFixHeroes().then(() => {
    console.log('🎉 Final heroes fix complete!');
    process.exit(0);
}).catch(error => {
    console.error('💥 Final fix failed:', error);
    process.exit(1);
});
