<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whiteout Survival - State 1661 Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        /* Fire Nation x Whiteout Survival Theme - EXACT COPY */
        :root {
            --fire-red: #dc2626;
            --fire-orange: #ea580c;
            --fire-yellow: #f59e0b;
            --fire-gold: #d97706;
            --ice-blue: #0ea5e9;
            --ice-cyan: #06b6d4;
            --ice-white: #f0f9ff;
            --dark-ember: #1f2937;
            --ash-gray: #374151;
            --smoke-gray: #6b7280;
        }

        body {
            background: linear-gradient(135deg,
                #0f172a 0%,
                #1e293b 25%,
                #374151 50%,
                #1f2937 75%,
                #111827 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
            position: relative;
        }

        /* Fire accent overlay */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 20% 80%, rgba(220, 38, 38, 0.08) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(234, 88, 12, 0.06) 0%, transparent 50%);
            pointer-events: none;
            z-index: 0;
        }

        @media (min-width: 768px) {
            body {
                background-size: 300% 300%;
                animation: subtleGradient 30s ease infinite;
            }
        }

        @keyframes subtleGradient {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .fire-glass {
            background: rgba(31, 41, 55, 0.9);
            border: 1px solid rgba(245, 158, 11, 0.3);
            position: relative;
            z-index: 1;
        }

        @media (min-width: 768px) {
            .fire-glass {
                background: linear-gradient(135deg,
                    rgba(31, 41, 55, 0.85) 0%,
                    rgba(55, 65, 81, 0.8) 50%,
                    rgba(17, 24, 39, 0.9) 100%);
                backdrop-filter: blur(10px);
                box-shadow:
                    0 0 15px rgba(0, 0, 0, 0.2),
                    inset 0 1px 0 rgba(245, 158, 11, 0.2);
            }

            .fire-glass:hover {
                border-color: rgba(245, 158, 11, 0.5);
                box-shadow:
                    0 0 20px rgba(0, 0, 0, 0.3),
                    inset 0 1px 0 rgba(245, 158, 11, 0.3);
            }
        }

        .fire-nation-title {
            font-family: 'Cinzel', serif;
            color: #f59e0b !important;
            background: none !important;
            -webkit-text-fill-color: #f59e0b !important;
            text-shadow:
                0 0 8px rgba(245, 158, 11, 0.8),
                0 0 16px rgba(220, 38, 38, 0.6),
                0 0 24px rgba(234, 88, 12, 0.4);
        }

        @media (min-width: 768px) {
            .fire-nation-title {
                text-shadow:
                    0 0 10px rgba(245, 158, 11, 0.9),
                    0 0 20px rgba(220, 38, 38, 0.7),
                    0 0 30px rgba(234, 88, 12, 0.5),
                    0 0 40px rgba(14, 165, 233, 0.3);
                animation: fireTextGlow 4s ease-in-out infinite;
            }
        }

        @keyframes fireTextGlow {
            0%, 100% {
                text-shadow:
                    0 0 10px rgba(245, 158, 11, 0.9),
                    0 0 20px rgba(220, 38, 38, 0.7),
                    0 0 30px rgba(234, 88, 12, 0.5);
            }
            50% {
                text-shadow:
                    0 0 15px rgba(245, 158, 11, 1),
                    0 0 25px rgba(220, 38, 38, 0.8),
                    0 0 35px rgba(234, 88, 12, 0.6),
                    0 0 45px rgba(14, 165, 233, 0.4);
            }
        }

        .fire-icon {
            color: #f59e0b;
        }

        @media (min-width: 768px) {
            .fire-icon {
                filter: drop-shadow(0 0 8px rgba(245, 158, 11, 0.4));
                animation: iconFlicker 3s ease-in-out infinite;
            }
        }

        @keyframes iconFlicker {
            0%, 100% {
                filter: drop-shadow(0 0 8px rgba(245, 158, 11, 0.4));
            }
            50% {
                filter: drop-shadow(0 0 12px rgba(220, 38, 38, 0.6));
            }
        }

        /* Event Type Buttons - EXACT STYLING */
        .event-type-btn {
            padding: 12px 16px;
            border-radius: 8px;
            border: 2px solid rgba(245, 158, 11, 0.3);
            background: rgba(55, 65, 81, 0.8);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 14px;
            font-weight: 500;
            display: block;
        }

        .event-type-btn:hover {
            border-color: rgba(245, 158, 11, 0.6);
            background: rgba(55, 65, 81, 1);
            transform: translateY(-2px);
        }

        .event-type-btn.active {
            border-color: #f59e0b;
            background: linear-gradient(45deg, #dc2626, #ea580c);
            box-shadow: 0 0 15px rgba(245, 158, 11, 0.4);
        }

        .event-type-btn i {
            display: block;
            font-size: 20px;
            margin-bottom: 8px;
            color: #f59e0b;
        }

        .event-type-btn.active i {
            color: white;
        }

        /* Tier System Styling - EXACT COPY */
        .tier-legend {
            padding: 8px 12px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 12px;
        }

        .tier-s-plus { background: linear-gradient(45deg, #dc2626, #ef4444); color: white; }
        .tier-s { background: linear-gradient(45deg, #ea580c, #f97316); color: white; }
        .tier-a-plus { background: linear-gradient(45deg, #f59e0b, #fbbf24); color: black; }
        .tier-a { background: linear-gradient(45deg, #eab308, #facc15); color: black; }
        .tier-b-plus { background: linear-gradient(45deg, #22c55e, #4ade80); color: black; }
        .tier-b { background: linear-gradient(45deg, #10b981, #34d399); color: black; }
        .tier-c { background: linear-gradient(45deg, #06b6d4, #22d3ee); color: black; }
        .tier-dont-use { background: linear-gradient(45deg, #6b7280, #9ca3af); color: white; }

        .tier-label {
            padding: 12px 16px;
            border-radius: 8px;
            font-weight: bold;
            font-size: 16px;
            min-width: 80px;
            text-align: center;
            color: white;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .hero-slot {
            width: 80px;
            height: 80px;
            border: 2px dashed rgba(245, 158, 11, 0.5);
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(55, 65, 81, 0.8);
            position: relative;
        }

        .hero-slot:hover {
            border-color: rgba(245, 158, 11, 0.8);
            background: rgba(55, 65, 81, 1);
            transform: translateY(-2px);
        }

        .hero-slot.filled {
            border: 2px solid rgba(245, 158, 11, 0.8);
            background: rgba(31, 41, 55, 0.9);
        }

        .hero-slot.filled:hover {
            border-color: #f59e0b;
            box-shadow: 0 0 15px rgba(245, 158, 11, 0.3);
        }

        .hero-image {
            width: 70px;
            height: 70px;
            border-radius: 8px;
            object-fit: cover;
        }

        .hero-type-icon {
            position: absolute;
            bottom: -8px;
            right: -8px;
            background: linear-gradient(45deg, #dc2626, #ea580c);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            border: 2px solid #1f2937;
        }

        .hero-name {
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 10px;
            color: #f59e0b;
            font-weight: bold;
            white-space: nowrap;
            max-width: 80px;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Admin elements */
        .admin-only {
            display: none;
        }

        .admin-only.visible {
            display: block;
        }
    </style>
</head>
<body class="text-white">
    <!-- Header -->
    <header class="fire-glass sticky top-0 z-40 p-4">
        <div class="container mx-auto flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <i class="fas fa-fire fire-icon text-2xl"></i>
                <h1 class="text-xl md:text-2xl font-bold fire-nation-title" id="mainTitle">FNA - State 1661</h1>
            </div>
            
            <!-- Desktop Navigation -->
            <nav class="hidden md:flex space-x-6">
                <a href="#heroes" class="hover:text-yellow-300 transition-colors group">
                    <i class="fas fa-users text-orange-400 mr-2 group-hover:text-yellow-300"></i>Heroes
                </a>
                <a href="#events" class="hover:text-yellow-300 transition-colors group">
                    <i class="fas fa-calendar text-orange-400 mr-2 group-hover:text-yellow-300"></i>Events
                </a>
                <a href="#stats" class="hover:text-yellow-300 transition-colors group">
                    <i class="fas fa-chart-bar text-orange-400 mr-2 group-hover:text-yellow-300"></i>Stats
                </a>
                <a href="#admin" id="adminNavLink" class="hidden hover:text-yellow-300 transition-colors group admin-only">
                    <i class="fas fa-cog text-orange-400 mr-2 group-hover:text-yellow-300"></i>Admin
                </a>
            </nav>
            
            <!-- User Menu -->
            <div class="flex items-center space-x-3">
                <button id="loginBtn" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-sign-in-alt text-gray-400 mr-2"></i>Login
                </button>
                <div id="userMenu" class="hidden">
                    <span id="userName" class="mr-3"></span>
                    <button id="logoutBtn" class="bg-red-600 hover:bg-red-700 px-3 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sign-out-alt text-gray-400"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Welcome Section -->
        <section class="fire-glass rounded-xl p-6 mb-8">
            <h2 class="text-2xl md:text-3xl font-bold mb-4 text-center fire-nation-title">
                <i class="fas fa-fire fire-icon mr-3"></i>
                <span id="welcomeTitle">Welcome to FNA - Fire Nation</span>
            </h2>
            <p class="text-center text-orange-100 text-lg">
                <span id="welcomeSubtitle">🔥 Dominating State 1661 with Fire & Ice ❄️</span>
            </p>
            <p class="text-center text-yellow-200 text-sm mt-2">
                <span id="welcomeQuote">"Fire is the element of power" - Avatar Universe</span>
            </p>
        </section>

        <!-- Hero Rankings Section -->
        <section id="heroes" class="fire-glass rounded-xl p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl md:text-2xl font-bold fire-nation-title">
                    <i class="fas fa-crown fire-icon mr-3"></i>
                    <span id="heroesTitle">Hero Tier System</span>
                </h3>
                <div class="flex space-x-2">
                    <button id="manageHeroesBtn" class="admin-only bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 px-4 py-2 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl">
                        <i class="fas fa-cog text-yellow-200 mr-2"></i>Manage Heroes
                    </button>
                </div>
            </div>

            <!-- Event Type Selector -->
            <div class="mb-6 p-4 bg-gray-800 bg-opacity-50 rounded-lg">
                <h4 class="font-bold mb-3 text-orange-200">Select Event Type</h4>
                <div class="grid grid-cols-2 md:grid-cols-3 gap-3" id="eventTypeSelector">
                    <!-- Event type buttons will be generated here -->
                </div>
            </div>

            <!-- Tier Legend -->
            <div class="mb-6 p-4 bg-gray-800 bg-opacity-50 rounded-lg">
                <h4 class="font-bold mb-3 text-orange-200">
                    <span id="currentEventTitle">Formation Guide: Infantry + Lancer + Marksman</span>
                </h4>
                <div class="grid grid-cols-4 md:grid-cols-8 gap-2 text-center text-sm">
                    <div class="tier-legend tier-s-plus">S+</div>
                    <div class="tier-legend tier-s">S</div>
                    <div class="tier-legend tier-a-plus">A+</div>
                    <div class="tier-legend tier-a">A</div>
                    <div class="tier-legend tier-b-plus">B+</div>
                    <div class="tier-legend tier-b">B</div>
                    <div class="tier-legend tier-c">C</div>
                    <div class="tier-legend tier-dont-use">Don't Use</div>
                </div>
            </div>

            <!-- Tier System -->
            <div id="heroTierSystem" class="space-y-6">
                <!-- Tiers will be dynamically generated -->
            </div>
        </section>

        <!-- Events Section -->
        <section id="events" class="fire-glass rounded-xl p-6 mb-8">
            <h3 class="text-xl md:text-2xl font-bold mb-6 fire-nation-title">
                <i class="fas fa-calendar-alt fire-icon mr-3"></i>
                <span id="eventsTitle">Current Events</span>
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="bg-blue-800 bg-opacity-50 rounded-lg p-4">
                    <h4 class="font-bold text-lg mb-2">Alliance War</h4>
                    <p class="text-blue-200">Prepare your best heroes for the upcoming alliance war!</p>
                    <div class="mt-3 text-sm text-blue-300">
                        <i class="fas fa-clock text-gray-400 mr-2"></i>
                        Starts in 2 days
                    </div>
                </div>
                <div class="bg-purple-800 bg-opacity-50 rounded-lg p-4">
                    <h4 class="font-bold text-lg mb-2">Hero Recruitment</h4>
                    <p class="text-purple-200">Special recruitment event with increased rates!</p>
                    <div class="mt-3 text-sm text-purple-300">
                        <i class="fas fa-clock text-gray-400 mr-2"></i>
                        3 days remaining
                    </div>
                </div>
            </div>
        </section>

        <!-- Stats Section -->
        <section id="stats" class="fire-glass rounded-xl p-6 mb-8">
            <h3 class="text-xl md:text-2xl font-bold mb-6 fire-nation-title">
                <i class="fas fa-chart-line fire-icon mr-3"></i>
                <span id="statsTitle">Alliance Statistics</span>
            </h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center">
                    <div class="text-2xl md:text-3xl font-bold text-blue-300">1,247</div>
                    <div class="text-sm text-blue-200">Active Players</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl md:text-3xl font-bold text-green-300">89</div>
                    <div class="text-sm text-green-200">Alliances</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl md:text-3xl font-bold text-yellow-300">156M</div>
                    <div class="text-sm text-yellow-200">Total Power</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl md:text-3xl font-bold text-red-300">42</div>
                    <div class="text-sm text-red-200">Days Active</div>
                </div>
            </div>
        </section>
    </main>

    <!-- Scripts -->
    <script src="js/dashboard-new.js"></script>
</body>
</html>
