const express = require('express');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = 5200;

// Middleware
app.use(express.json());
app.use(express.static('.'));

// CORS for all origins
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});

// Data storage
const DATA_FILE = 'dashboard-data.json';

// Initialize data file if it doesn't exist
function initializeData() {
    if (!fs.existsSync(DATA_FILE)) {
        const defaultData = {
            heroes: [
                {
                    id: 'hero-1',
                    name: '<PERSON>',
                    type: 'infantry',
                    image: null,
                    expeditionSkills: [
                        { name: 'Ice Shield', description: 'Reduces incoming damage' },
                        { name: 'Frost Bite', description: 'Slows enemy movement' },
                        { name: 'Blizzard', description: 'Area damage over time' }
                    ]
                },
                {
                    id: 'hero-2',
                    name: 'Blaze',
                    type: 'marksman',
                    image: null,
                    expeditionSkills: [
                        { name: 'Fire Arrow', description: 'Burning damage over time' },
                        { name: 'Eagle Eye', description: 'Increased accuracy' },
                        { name: 'Explosive Shot', description: 'Area damage on impact' }
                    ]
                },
                {
                    id: 'hero-3',
                    name: 'Storm',
                    type: 'lancer',
                    image: null,
                    expeditionSkills: [
                        { name: 'Lightning Strike', description: 'High single target damage' },
                        { name: 'Thunder Roar', description: 'Stuns nearby enemies' },
                        { name: 'Wind Dash', description: 'Increased movement speed' }
                    ]
                }
            ],
            tierData: {
                'bear-captain': {
                    'S+': [
                        ['hero-1', 'hero-2', 'hero-3']
                    ],
                    'S': [
                        ['hero-1', 'hero-2']
                    ]
                },
                'bear-joiner': {
                    'S+': [
                        ['hero-1']
                    ]
                }
            },
            editableTexts: {
                'welcome-title': 'Welcome to FNA - Fire Nation',
                'welcome-subtitle': '🔥 Dominating State 1661 with Fire & Ice ❄️',
                'welcome-quote': '"Fire is the element of power" - Avatar Universe',
                'heroes-title': 'Hero Tier System',
                'events-title': 'Current Events',
                'stats-title': 'Alliance Statistics'
            },
            users: {
                'admin': {
                    id: 'admin',
                    username: 'admin',
                    password: 'fna2024',
                    role: 'admin',
                    displayName: 'FNA Admin'
                }
            }
        };
        fs.writeFileSync(DATA_FILE, JSON.stringify(defaultData, null, 2));
    }
}

// Load data
function loadData() {
    try {
        return JSON.parse(fs.readFileSync(DATA_FILE, 'utf8'));
    } catch (error) {
        console.error('Error loading data:', error);
        initializeData();
        return JSON.parse(fs.readFileSync(DATA_FILE, 'utf8'));
    }
}

// Save data
function saveData(data) {
    try {
        fs.writeFileSync(DATA_FILE, JSON.stringify(data, null, 2));
        return true;
    } catch (error) {
        console.error('Error saving data:', error);
        return false;
    }
}

// API Routes
app.get('/api/data', (req, res) => {
    const data = loadData();
    res.json(data);
});

app.post('/api/data', (req, res) => {
    const success = saveData(req.body);
    res.json({ success });
});

app.post('/api/login', (req, res) => {
    const { username, password } = req.body;
    const data = loadData();
    
    const user = data.users[username];
    if (user && user.password === password) {
        res.json({ 
            success: true, 
            user: { 
                username: user.username, 
                role: user.role, 
                displayName: user.displayName 
            } 
        });
    } else {
        res.json({ success: false, message: 'Invalid credentials' });
    }
});

// Serve main page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Initialize and start server
initializeData();

app.listen(PORT, '0.0.0.0', () => {
    console.log(`🔥 Clean Dashboard Server running on port ${PORT}`);
    console.log(`🌐 Local: http://localhost:${PORT}`);
    console.log(`🌐 Network: http://************:${PORT}`);
    console.log(`❄️ State 1661 Dashboard is ready!`);
});
