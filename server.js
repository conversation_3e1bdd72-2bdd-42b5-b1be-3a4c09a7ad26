const express = require('express');
const path = require('path');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const fs = require('fs');
const WebSocket = require('ws');
const https = require('https');

const app = express();
const PORT = process.env.PORT || 5200;

// Security middleware with relaxed settings for development
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://cdn.tailwindcss.com", "https://cdnjs.cloudflare.com", "https://fonts.googleapis.com"],
            scriptSrc: ["'self'", "'unsafe-inline'", "https://cdn.tailwindcss.com", "https://cdn.jsdelivr.net"],
            imgSrc: ["'self'", "data:", "https:", "blob:"],
            connectSrc: ["'self'", "wss:", "https:"],
            fontSrc: ["'self'", "https://fonts.gstatic.com", "https://cdnjs.cloudflare.com"],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            frameSrc: ["'none'"],
        },
    },
    crossOriginOpenerPolicy: false,
    crossOriginEmbedderPolicy: false
}));

// CORS configuration
app.use(cors({
    origin: ['https://localhost:5200', 'https://127.0.0.1:5200', 'https://************:5200'],
    credentials: true
}));

// Compression middleware
app.use(compression());

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));
app.use('/css', express.static(path.join(__dirname, 'css')));
app.use('/js', express.static(path.join(__dirname, 'js')));
app.use('/images', express.static(path.join(__dirname, 'images')));

// Data management
const DATA_FILE = path.join(__dirname, 'server-data.json');

// Initialize or load data
let serverData = {
    heroes: [],
    tierData: {},
    events: [],
    users: {
        admin: {
            username: 'admin',
            password: 'fna2024',
            role: 'admin'
        }
    },
    editableTexts: {}
};

// Load data if exists
if (fs.existsSync(DATA_FILE)) {
    try {
        serverData = JSON.parse(fs.readFileSync(DATA_FILE, 'utf8'));
    } catch (error) {
        console.error('Error loading data:', error);
    }
}

// Save data function
function saveData() {
    try {
        fs.writeFileSync(DATA_FILE, JSON.stringify(serverData, null, 2));
    } catch (error) {
        console.error('Error saving data:', error);
    }
}

// API Routes
app.get('/api/data', (req, res) => {
    // Check if request is from localhost
    const isLocalhost = req.hostname === 'localhost' || req.hostname === '127.0.0.1';
    
    // If localhost, automatically set admin session
    if (isLocalhost) {
        res.json({
            ...serverData,
            currentUser: {
                username: 'localhost-admin',
                role: 'admin'
            }
        });
    } else {
        res.json(serverData);
    }
});

app.post('/api/data', (req, res) => {
    const updates = req.body;
    
    // Update server data
    Object.assign(serverData, updates);
    
    // Save to file
    saveData();
    
    // Broadcast to all clients
    wss.clients.forEach(client => {
        if (client.readyState === WebSocket.OPEN) {
            client.send(JSON.stringify({
                type: 'data-update',
                data: serverData
            }));
        }
    });
    
    res.json({ success: true });
});

// Serve main application
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Create HTTPS server
const server = https.createServer({
    key: fs.readFileSync('key.pem'),
    cert: fs.readFileSync('cert.pem')
}, app);

// WebSocket server
const wss = new WebSocket.Server({ server });

wss.on('connection', (ws, req) => {
    console.log('New WebSocket connection');
    
    // Send initial data
    ws.send(JSON.stringify({
        type: 'initial-data',
        data: serverData
    }));
    
    ws.on('message', (message) => {
        try {
            const data = JSON.parse(message);
            if (data.type === 'data-update') {
                Object.assign(serverData, data.data);
                saveData();
                
                // Broadcast to all other clients
                wss.clients.forEach(client => {
                    if (client !== ws && client.readyState === WebSocket.OPEN) {
                        client.send(JSON.stringify({
                            type: 'data-update',
                            data: serverData
                        }));
                    }
                });
            }
        } catch (error) {
            console.error('WebSocket message error:', error);
        }
    });
});

// Start server
server.listen(PORT, '0.0.0.0', () => {
    console.log(`🏔️  Whiteout Survival Dashboard running on port ${PORT}`);
    console.log(`🌐 Local: https://localhost:${PORT}`);
    console.log(`🌐 Network: https://************:${PORT}`);
    console.log(`❄️  State 1661 Dashboard is ready!`);
});

module.exports = app;
