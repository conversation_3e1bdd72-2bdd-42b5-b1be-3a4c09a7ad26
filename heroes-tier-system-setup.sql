-- Whiteout Survival Heroes Tier System Database Setup

-- Drop existing heroes table and recreate with proper structure
DROP TABLE IF EXISTS hero_uploads;
DROP TABLE IF EXISTS heroes;

-- Create heroes table with proper tier system
CREATE TABLE heroes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    generation ENUM('1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', 'rare', 'epic') NOT NULL,
    type ENUM('infantry', 'marksman', 'lancer') NOT NULL,
    image_url VARCHAR(255),
    description TEXT,
    skills JSON,
    stats JSON,
    tier_ranking INT DEFAULT 0,
    rarity ENUM('common', 'rare', 'epic', 'legendary') DEFAULT 'common',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_generation (generation),
    INDEX idx_type (type),
    INDEX idx_rarity (rarity),
    INDEX idx_tier_ranking (tier_ranking)
);

-- Insert Generation 1 Heroes
INSERT INTO heroes (name, generation, type, image_url, description, skills, stats, tier_ranking, rarity) VALUES
('Bahiti', '1', 'infantry', 'images/heroes/bahiti.png', 'Desert warrior with sand-based abilities', 
 JSON_ARRAY('Desert Storm', 'Sand Shield', 'Mirage Strike'), 
 JSON_OBJECT('attack', 850, 'defense', 920, 'health', 12500), 1, 'common'),

('Flint', '1', 'marksman', 'images/heroes/flint.png', 'Expert marksman with precision abilities',
 JSON_ARRAY('Precision Shot', 'Eagle Eye', 'Explosive Arrow'),
 JSON_OBJECT('attack', 1200, 'defense', 650, 'health', 8500), 2, 'common'),

('Natalia', '1', 'lancer', 'images/heroes/natalia.png', 'Cavalry commander with mounted combat skills',
 JSON_ARRAY('Cavalry Charge', 'Battle Fury', 'Lance Strike'),
 JSON_OBJECT('attack', 980, 'defense', 780, 'health', 10200), 3, 'common');

-- Insert Generation 2 Heroes
INSERT INTO heroes (name, generation, type, image_url, description, skills, stats, tier_ranking, rarity) VALUES
('Molly', '2', 'infantry', 'images/heroes/molly.png', 'Defensive specialist with shield abilities',
 JSON_ARRAY('Shield Wall', 'Defensive Stance', 'Counter Attack'),
 JSON_OBJECT('attack', 720, 'defense', 1100, 'health', 14000), 4, 'common'),

('Patrick', '2', 'marksman', 'images/heroes/patrick.png', 'Stealth marksman with sniper abilities',
 JSON_ARRAY('Sniper Shot', 'Stealth', 'Critical Strike'),
 JSON_OBJECT('attack', 1350, 'defense', 580, 'health', 7800), 5, 'common'),

('Wayne', '2', 'lancer', 'images/heroes/wayne.png', 'Mounted warrior with lance combat expertise',
 JSON_ARRAY('Lance Strike', 'Mounted Combat', 'Charge Attack'),
 JSON_OBJECT('attack', 1050, 'defense', 720, 'health', 9800), 6, 'common');

-- Insert Generation 3 Heroes
INSERT INTO heroes (name, generation, type, image_url, description, skills, stats, tier_ranking, rarity) VALUES
('Jessie', '3', 'infantry', 'images/heroes/jessie.png', 'Berserker with rage-based abilities',
 JSON_ARRAY('Berserker Rage', 'Iron Will', 'Fury Strike'),
 JSON_OBJECT('attack', 1100, 'defense', 850, 'health', 11500), 7, 'common'),

('Zoe', '3', 'marksman', 'images/heroes/zoe.png', 'Multi-shot specialist with hunter abilities',
 JSON_ARRAY('Multi-Shot', 'Hunter\'s Mark', 'Piercing Arrow'),
 JSON_OBJECT('attack', 1280, 'defense', 620, 'health', 8200), 8, 'common'),

('Gina', '3', 'lancer', 'images/heroes/gina.png', 'Combat master with spear expertise',
 JSON_ARRAY('Spear Thrust', 'Combat Mastery', 'Whirlwind Attack'),
 JSON_OBJECT('attack', 1150, 'defense', 800, 'health', 10500), 9, 'common');

-- Insert Generation 4 Heroes
INSERT INTO heroes (name, generation, type, image_url, description, skills, stats, tier_ranking, rarity) VALUES
('Reginald', '4', 'infantry', 'images/heroes/reginald.png', 'Royal guard with noble combat skills',
 JSON_ARRAY('Royal Guard', 'Noble Strike', 'Honor Shield'),
 JSON_OBJECT('attack', 1200, 'defense', 950, 'health', 13000), 10, 'common'),

('Cloris', '4', 'marksman', 'images/heroes/cloris.png', 'Elven archer with nature magic',
 JSON_ARRAY('Nature\'s Arrow', 'Forest Blessing', 'Wind Shot'),
 JSON_OBJECT('attack', 1400, 'defense', 600, 'health', 8800), 11, 'common'),

('Alonso', '4', 'lancer', 'images/heroes/alonso.png', 'Knight with heavy armor and lance',
 JSON_ARRAY('Heavy Charge', 'Armor Break', 'Knight\'s Honor'),
 JSON_OBJECT('attack', 1180, 'defense', 850, 'health', 11200), 12, 'common');

-- Insert Generation 5 Heroes
INSERT INTO heroes (name, generation, type, image_url, description, skills, stats, tier_ranking, rarity) VALUES
('Sergey', '5', 'infantry', 'images/heroes/sergey.png', 'Russian warrior with ice abilities',
 JSON_ARRAY('Ice Strike', 'Frozen Shield', 'Winter\'s Wrath'),
 JSON_OBJECT('attack', 1250, 'defense', 980, 'health', 13500), 13, 'common'),

('Hilda', '5', 'marksman', 'images/heroes/hilda.png', 'Viking archer with frost arrows',
 JSON_ARRAY('Frost Arrow', 'Ice Storm', 'Nordic Aim'),
 JSON_OBJECT('attack', 1450, 'defense', 620, 'health', 9000), 14, 'common'),

('Brooke', '5', 'lancer', 'images/heroes/brooke.png', 'Ice lancer with freezing attacks',
 JSON_ARRAY('Freeze Lance', 'Ice Barrier', 'Glacial Charge'),
 JSON_OBJECT('attack', 1220, 'defense', 880, 'health', 11800), 15, 'common');

-- Insert Generation 6 Heroes
INSERT INTO heroes (name, generation, type, image_url, description, skills, stats, tier_ranking, rarity) VALUES
('Jeronimo', '6', 'infantry', 'images/heroes/jeronimo.png', 'Apache warrior with tribal magic',
 JSON_ARRAY('Tribal War Cry', 'Spirit Shield', 'Ancestral Power'),
 JSON_OBJECT('attack', 1300, 'defense', 1020, 'health', 14000), 16, 'common'),

('Zinman', '6', 'marksman', 'images/heroes/zinman.png', 'Sharpshooter with explosive rounds',
 JSON_ARRAY('Explosive Shot', 'Rapid Fire', 'Demolition'),
 JSON_OBJECT('attack', 1500, 'defense', 640, 'health', 9200), 17, 'common'),

('Miho', '6', 'lancer', 'images/heroes/miho.png', 'Samurai with katana mastery',
 JSON_ARRAY('Katana Slash', 'Bushido Spirit', 'Honor Strike'),
 JSON_OBJECT('attack', 1280, 'defense', 920, 'health', 12200), 18, 'common');

-- Insert Generation 7 Heroes
INSERT INTO heroes (name, generation, type, image_url, description, skills, stats, tier_ranking, rarity) VALUES
('Rusty', '7', 'infantry', 'images/heroes/rusty.png', 'Mechanic with robotic enhancements',
 JSON_ARRAY('Mech Suit', 'Repair Protocol', 'Steel Fist'),
 JSON_OBJECT('attack', 1350, 'defense', 1080, 'health', 14500), 19, 'common'),

('Maddie', '7', 'marksman', 'images/heroes/maddie.png', 'Tech sniper with laser weapons',
 JSON_ARRAY('Laser Sight', 'Energy Blast', 'Tech Overload'),
 JSON_OBJECT('attack', 1550, 'defense', 660, 'health', 9500), 20, 'common'),

('Kelvins', '7', 'lancer', 'images/heroes/kelvins.png', 'Cyber knight with plasma lance',
 JSON_ARRAY('Plasma Lance', 'Cyber Shield', 'Digital Strike'),
 JSON_OBJECT('attack', 1320, 'defense', 960, 'health', 12600), 21, 'common');

-- Insert Generation 8 Heroes
INSERT INTO heroes (name, generation, type, image_url, description, skills, stats, tier_ranking, rarity) VALUES
('Yuki', '8', 'infantry', 'images/heroes/yuki.png', 'Snow ninja with stealth abilities',
 JSON_ARRAY('Shadow Clone', 'Ice Shuriken', 'Ninja Vanish'),
 JSON_OBJECT('attack', 1400, 'defense', 1120, 'health', 15000), 22, 'common'),

('Tracey', '8', 'marksman', 'images/heroes/tracey.png', 'Elite sniper with thermal vision',
 JSON_ARRAY('Thermal Scope', 'Piercing Shot', 'Headhunter'),
 JSON_OBJECT('attack', 1600, 'defense', 680, 'health', 9800), 23, 'common'),

('Jasser', '8', 'lancer', 'images/heroes/jasser.png', 'Desert warrior with scimitar',
 JSON_ARRAY('Scimitar Dance', 'Desert Wind', 'Mirage Strike'),
 JSON_OBJECT('attack', 1380, 'defense', 1000, 'health', 13000), 24, 'common');

-- Insert Generation 9 Heroes
INSERT INTO heroes (name, generation, type, image_url, description, skills, stats, tier_ranking, rarity) VALUES
('Bahiti', '9', 'infantry', 'images/heroes/bahiti_gen9.png', 'Enhanced desert warrior with advanced sand magic',
 JSON_ARRAY('Greater Desert Storm', 'Sandstorm Shield', 'Dune Master'),
 JSON_OBJECT('attack', 1450, 'defense', 1180, 'health', 15500), 25, 'common'),

('Flint', '9', 'marksman', 'images/heroes/flint_gen9.png', 'Master marksman with legendary precision',
 JSON_ARRAY('Perfect Shot', 'Eagle\'s Vision', 'Explosive Mastery'),
 JSON_OBJECT('attack', 1650, 'defense', 700, 'health', 10200), 26, 'common'),

('Natalia', '9', 'lancer', 'images/heroes/natalia_gen9.png', 'Elite cavalry commander with war experience',
 JSON_ARRAY('War Charge', 'Battle Commander', 'Victory Lance'),
 JSON_OBJECT('attack', 1420, 'defense', 1040, 'health', 13500), 27, 'common');

-- Insert Generation 10 Heroes
INSERT INTO heroes (name, generation, type, image_url, description, skills, stats, tier_ranking, rarity) VALUES
('Molly', '10', 'infantry', 'images/heroes/molly_gen10.png', 'Ultimate defensive specialist with fortress abilities',
 JSON_ARRAY('Fortress Wall', 'Unbreakable Defense', 'Guardian\'s Resolve'),
 JSON_OBJECT('attack', 1500, 'defense', 1250, 'health', 16000), 28, 'common'),

('Patrick', '10', 'marksman', 'images/heroes/patrick_gen10.png', 'Shadow master with invisible strikes',
 JSON_ARRAY('Shadow Shot', 'Phantom Strike', 'Assassin\'s Mark'),
 JSON_OBJECT('attack', 1700, 'defense', 720, 'health', 10500), 29, 'common'),

('Wayne', '10', 'lancer', 'images/heroes/wayne_gen10.png', 'Legendary mounted warrior with divine lance',
 JSON_ARRAY('Divine Lance', 'Celestial Charge', 'Holy Strike'),
 JSON_OBJECT('attack', 1480, 'defense', 1080, 'health', 14000), 30, 'common');

-- Insert Generation 11 Heroes
INSERT INTO heroes (name, generation, type, image_url, description, skills, stats, tier_ranking, rarity) VALUES
('Jessie', '11', 'infantry', 'images/heroes/jessie_gen11.png', 'Apex berserker with unstoppable rage',
 JSON_ARRAY('Unstoppable Rage', 'Berserker\'s Fury', 'Rampage'),
 JSON_OBJECT('attack', 1550, 'defense', 1300, 'health', 16500), 31, 'common'),

('Zoe', '11', 'marksman', 'images/heroes/zoe_gen11.png', 'Master hunter with multi-dimensional arrows',
 JSON_ARRAY('Dimensional Shot', 'Hunter\'s Mastery', 'Arrow Storm'),
 JSON_OBJECT('attack', 1750, 'defense', 740, 'health', 11000), 32, 'common'),

('Gina', '11', 'lancer', 'images/heroes/gina_gen11.png', 'Spear saint with transcendent combat skills',
 JSON_ARRAY('Transcendent Spear', 'Combat Saint', 'Ultimate Strike'),
 JSON_OBJECT('attack', 1520, 'defense', 1120, 'health', 14500), 33, 'common');

-- Insert Rare Heroes (Multiple of same type allowed)
INSERT INTO heroes (name, generation, type, image_url, description, skills, stats, tier_ranking, rarity) VALUES
('Frost Guardian', 'rare', 'infantry', 'images/heroes/frost_guardian.png', 'Legendary ice warrior with eternal frost powers',
 JSON_ARRAY('Eternal Frost', 'Ice Fortress', 'Glacial Dominion'),
 JSON_OBJECT('attack', 1800, 'defense', 1500, 'health', 18000), 34, 'rare'),

('Ice Sentinel', 'rare', 'infantry', 'images/heroes/ice_sentinel.png', 'Ancient guardian of the frozen realm',
 JSON_ARRAY('Frozen Realm', 'Ice Prison', 'Arctic Shield'),
 JSON_OBJECT('attack', 1750, 'defense', 1600, 'health', 19000), 35, 'rare'),

('Blizzard Archer', 'rare', 'marksman', 'images/heroes/blizzard_archer.png', 'Master of ice arrows and winter storms',
 JSON_ARRAY('Blizzard Arrow', 'Winter Storm', 'Absolute Zero'),
 JSON_OBJECT('attack', 2000, 'defense', 800, 'health', 12000), 36, 'rare'),

('Frost Sniper', 'rare', 'marksman', 'images/heroes/frost_sniper.png', 'Elite marksman with crystalline precision',
 JSON_ARRAY('Crystal Shot', 'Ice Shard', 'Frozen Precision'),
 JSON_OBJECT('attack', 2100, 'defense', 750, 'health', 11500), 37, 'rare'),

('Glacier Lance', 'rare', 'lancer', 'images/heroes/glacier_lance.png', 'Wielder of the legendary ice lance',
 JSON_ARRAY('Glacier Strike', 'Ice Lance Mastery', 'Frozen Charge'),
 JSON_OBJECT('attack', 1900, 'defense', 1200, 'health', 15000), 38, 'rare'),

('Winter Knight', 'rare', 'lancer', 'images/heroes/winter_knight.png', 'Noble knight of the eternal winter',
 JSON_ARRAY('Winter\'s Edge', 'Chivalrous Strike', 'Frost Armor'),
 JSON_OBJECT('attack', 1850, 'defense', 1300, 'health', 16000), 39, 'rare');

-- Insert Epic Heroes (Multiple of same type allowed)
INSERT INTO heroes (name, generation, type, image_url, description, skills, stats, tier_ranking, rarity) VALUES
('Inferno Warlord', 'epic', 'infantry', 'images/heroes/inferno_warlord.png', 'Legendary fire lord with apocalyptic power',
 JSON_ARRAY('Inferno Apocalypse', 'Flame Emperor', 'Hellfire Domain'),
 JSON_OBJECT('attack', 2500, 'defense', 2000, 'health', 25000), 40, 'epic'),

('Void Destroyer', 'epic', 'infantry', 'images/heroes/void_destroyer.png', 'Destroyer from the void realm',
 JSON_ARRAY('Void Annihilation', 'Reality Tear', 'Chaos Strike'),
 JSON_OBJECT('attack', 2600, 'defense', 1900, 'health', 24000), 41, 'epic'),

('Storm Empress', 'epic', 'marksman', 'images/heroes/storm_empress.png', 'Empress of lightning and thunder',
 JSON_ARRAY('Lightning Storm', 'Thunder Strike', 'Storm Dominion'),
 JSON_OBJECT('attack', 2800, 'defense', 1000, 'health', 15000), 42, 'epic'),

('Shadow Reaper', 'epic', 'marksman', 'images/heroes/shadow_reaper.png', 'Death incarnate with shadow arrows',
 JSON_ARRAY('Death Arrow', 'Shadow Realm', 'Soul Harvest'),
 JSON_OBJECT('attack', 2900, 'defense', 900, 'health', 14000), 43, 'epic'),

('Dragon Slayer', 'epic', 'lancer', 'images/heroes/dragon_slayer.png', 'Legendary hero who slayed the ancient dragon',
 JSON_ARRAY('Dragon Bane', 'Legendary Strike', 'Hero\'s Resolve'),
 JSON_OBJECT('attack', 2700, 'defense', 1500, 'health', 20000), 44, 'epic'),

('Celestial Spear', 'epic', 'lancer', 'images/heroes/celestial_spear.png', 'Divine warrior with heavenly spear',
 JSON_ARRAY('Celestial Strike', 'Divine Judgment', 'Heaven\'s Wrath'),
 JSON_OBJECT('attack', 2650, 'defense', 1600, 'health', 21000), 45, 'epic');
