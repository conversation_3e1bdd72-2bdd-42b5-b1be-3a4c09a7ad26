const express = require('express');
const path = require('path');
const fs = require('fs');
const cookieParser = require('cookie-parser');
const DatabaseManager = require('./js/database');

const app = express();
const PORT = 5200;

// Initialize database
const db = new DatabaseManager();

// Middleware
app.use(express.json());
app.use(express.static('.'));
app.use(cookieParser());

// CORS
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});

// Authentication middleware
async function authenticateSession(req, res, next) {
    const sessionId = req.cookies.session_id;
    
    if (sessionId) {
        const user = await db.validateSession(sessionId);
        if (user) {
            req.user = user;
        }
    }
    
    next();
}

// Apply auth middleware to all routes
app.use(authenticateSession);

// API Routes

// Login endpoint
app.post('/api/login', async (req, res) => {
    try {
        const { username, password } = req.body;
        console.log('🔥 Login attempt:', username);
        
        const user = await db.authenticateUser(username, password);
        
        if (user) {
            const sessionId = await db.createSession(user.id);
            
            if (sessionId) {
                res.cookie('session_id', sessionId, {
                    httpOnly: true,
                    secure: false, // Set to true in production with HTTPS
                    maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
                });
                
                console.log('🔥 Login successful for:', user.username);
                res.json({
                    success: true,
                    user: {
                        username: user.username,
                        role: user.role,
                        displayName: user.display_name
                    }
                });
            } else {
                res.status(500).json({ success: false, message: 'Session creation failed' });
            }
        } else {
            console.log('❄️ Login failed for:', username);
            res.status(401).json({ success: false, message: 'Invalid credentials' });
        }
    } catch (error) {
        console.error('❄️ Login error:', error);
        res.status(500).json({ success: false, message: 'Server error' });
    }
});

// Logout endpoint
app.post('/api/logout', async (req, res) => {
    try {
        const sessionId = req.cookies.session_id;
        
        if (sessionId) {
            await db.destroySession(sessionId);
        }
        
        res.clearCookie('session_id');
        res.json({ success: true });
    } catch (error) {
        console.error('❄️ Logout error:', error);
        res.status(500).json({ success: false, message: 'Server error' });
    }
});

// Check session endpoint
app.get('/api/session', (req, res) => {
    if (req.user) {
        res.json({
            authenticated: true,
            user: {
                username: req.user.username,
                role: req.user.role,
                displayName: req.user.display_name
            }
        });
    } else {
        res.json({ authenticated: false });
    }
});

// Get editable texts
app.get('/api/texts', async (req, res) => {
    try {
        const texts = await db.getEditableTexts();
        res.json(texts);
    } catch (error) {
        console.error('❄️ Error loading texts:', error);
        res.status(500).json({ error: 'Failed to load texts' });
    }
});

// Update editable text (admin only)
app.post('/api/texts', async (req, res) => {
    try {
        if (!req.user || (req.user.role !== 'admin' && req.user.role !== 'moderator')) {
            return res.status(403).json({ error: 'Unauthorized' });
        }
        
        const { fieldKey, fieldValue } = req.body;
        const success = await db.updateEditableText(fieldKey, fieldValue, req.user.user_id);
        
        if (success) {
            console.log('🔥 Text updated:', fieldKey, 'by', req.user.username);
            res.json({ success: true });
        } else {
            res.status(500).json({ error: 'Failed to update text' });
        }
    } catch (error) {
        console.error('❄️ Error updating text:', error);
        res.status(500).json({ error: 'Server error' });
    }
});

// Legacy data endpoint (for backward compatibility)
app.get('/api/data', async (req, res) => {
    try {
        // Load from JSON file for now, will migrate to database later
        const dataPath = path.join(__dirname, 'dashboard-data.json');
        
        if (fs.existsSync(dataPath)) {
            const data = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
            
            // Add editable texts from database
            const texts = await db.getEditableTexts();
            data.editableTexts = texts;
            
            res.json(data);
        } else {
            res.status(404).json({ error: 'Data file not found' });
        }
    } catch (error) {
        console.error('❄️ Error loading data:', error);
        res.status(500).json({ error: 'Failed to load data' });
    }
});

// Save data endpoint (for backward compatibility)
app.post('/api/data', (req, res) => {
    try {
        const dataPath = path.join(__dirname, 'dashboard-data.json');
        const data = req.body;
        
        // Remove editable texts from JSON (they're in database now)
        delete data.editableTexts;
        
        fs.writeFileSync(dataPath, JSON.stringify(data, null, 2));
        console.log('🔥 Data saved to JSON file');
        res.json({ success: true });
    } catch (error) {
        console.error('❄️ Error saving data:', error);
        res.status(500).json({ error: 'Failed to save data' });
    }
});

// User management endpoints (admin only)
app.get('/api/users', async (req, res) => {
    try {
        if (!req.user || req.user.role !== 'admin') {
            return res.status(403).json({ error: 'Admin access required' });
        }

        const users = await db.getAllUsers();
        res.json(users);
    } catch (error) {
        console.error('❄️ Error loading users:', error);
        res.status(500).json({ error: 'Failed to load users' });
    }
});

// Update user role (admin only)
app.post('/api/users/:userId/role', async (req, res) => {
    try {
        if (!req.user || req.user.role !== 'admin') {
            return res.status(403).json({ error: 'Admin access required' });
        }

        const { userId } = req.params;
        const { role } = req.body;

        const success = await db.updateUserRole(userId, role);

        if (success) {
            console.log('🔥 User role updated:', userId, 'to', role);
            res.json({ success: true });
        } else {
            res.status(500).json({ error: 'Failed to update user role' });
        }
    } catch (error) {
        console.error('❄️ Error updating user role:', error);
        res.status(500).json({ error: 'Server error' });
    }
});

// Create new user (admin only)
app.post('/api/users', async (req, res) => {
    try {
        if (!req.user || req.user.role !== 'admin') {
            return res.status(403).json({ error: 'Admin access required' });
        }

        const { username, password, displayName, role } = req.body;

        const userId = await db.createUser(username, password, displayName, role);

        if (userId) {
            console.log('🔥 User created:', username);
            res.json({ success: true, userId });
        } else {
            res.status(500).json({ error: 'Failed to create user' });
        }
    } catch (error) {
        console.error('❄️ Error creating user:', error);
        res.status(500).json({ error: 'Server error' });
    }
});

// Heroes API endpoints
app.get('/api/heroes', async (req, res) => {
    try {
        const heroes = await db.getAllHeroes();
        res.json(heroes);
    } catch (error) {
        console.error('❄️ Error loading heroes:', error);
        res.status(500).json({ error: 'Failed to load heroes' });
    }
});

app.post('/api/heroes', async (req, res) => {
    try {
        if (!req.user || (req.user.role !== 'admin' && req.user.role !== 'moderator')) {
            return res.status(403).json({ error: 'Unauthorized' });
        }

        const heroData = req.body;
        const heroId = await db.createHero(heroData, req.user.id);

        if (heroId) {
            console.log('🔥 Hero created:', heroData.name);
            res.json({ success: true, heroId });
        } else {
            res.status(500).json({ error: 'Failed to create hero' });
        }
    } catch (error) {
        console.error('❄️ Error creating hero:', error);
        res.status(500).json({ error: 'Server error' });
    }
});

// Error handling
app.use((error, req, res, next) => {
    console.error('❄️ Server error:', error);
    res.status(500).json({ error: 'Internal server error' });
});

// Initialize database with correct Whiteout Survival heroes
async function initializeHeroes() {
    try {
        console.log('🔥 Initializing database with correct Whiteout Survival heroes...');

        // Clear existing heroes first
        await db.pool.execute('DELETE FROM heroes');
        console.log('🔥 Database: Cleared existing heroes');

        // Correct Whiteout Survival heroes based on official data
        const heroes = [
            // RARE HEROES (Blue - R rarity)
            {
                name: 'Charlie',
                generation: 'rare',
                type: 'Lancer',
                rarity: 'rare',
                image_url: '/images/heroes/placeholder.png',
                description: 'Only use blue heroes for gathering resources',
                skills: [],
                stats: {
                    max_hero_stats: '90.1%',
                    exclusive_gear: '',
                    widget_bonus: ''
                },
                tier_ranking: null
            },
            {
                name: 'Cloris',
                generation: 'rare',
                type: 'Marksman',
                rarity: 'rare',
                image_url: '/images/heroes/placeholder.png',
                description: 'Only use blue heroes for gathering resources',
                skills: [],
                stats: {
                    max_hero_stats: '90.1%',
                    exclusive_gear: '',
                    widget_bonus: ''
                },
                tier_ranking: null
            },
            {
                name: 'Eugene',
                generation: 'rare',
                type: 'Infantry',
                rarity: 'rare',
                image_url: '/images/heroes/placeholder.png',
                description: 'Only use blue heroes for gathering resources',
                skills: [],
                stats: {
                    max_hero_stats: '90.1%',
                    exclusive_gear: '',
                    widget_bonus: ''
                },
                tier_ranking: null
            },
            {
                name: 'Smith',
                generation: 'rare',
                type: 'Infantry',
                rarity: 'rare',
                image_url: '/images/heroes/placeholder.png',
                description: 'Only use blue heroes for gathering resources',
                skills: [],
                stats: {
                    max_hero_stats: '90.1%',
                    exclusive_gear: '',
                    widget_bonus: ''
                },
                tier_ranking: null
            },

            // EPIC HEROES (Purple - SR rarity)
            {
                name: 'Bahiti',
                generation: 'epic',
                type: 'Marksman',
                rarity: 'epic',
                image_url: '/images/heroes/bahiti.png',
                description: '20% Damage reduction for troops',
                skills: [],
                stats: {
                    max_hero_stats: '140.1%',
                    exclusive_gear: '',
                    widget_bonus: ''
                },
                tier_ranking: null
            },
            {
                name: 'Gina',
                generation: 'epic',
                type: 'Marksman',
                rarity: 'epic',
                image_url: '/images/heroes/Gina.png',
                description: 'Epic hero with enhanced combat abilities',
                skills: [],
                stats: {
                    max_hero_stats: '110.1%',
                    exclusive_gear: '',
                    widget_bonus: ''
                },
                tier_ranking: null
            },
            {
                name: 'Jasser',
                generation: 'epic',
                type: 'Marksman',
                rarity: 'epic',
                image_url: '/images/heroes/jasser.jpg',
                description: '25% Increased troop damage',
                skills: [],
                stats: {
                    max_hero_stats: '140.1%',
                    exclusive_gear: '',
                    widget_bonus: ''
                },
                tier_ranking: null
            },
            {
                name: 'Jessie',
                generation: 'epic',
                type: 'Lancer',
                rarity: 'epic',
                image_url: '/images/heroes/jessie.png',
                description: '25% Increased troop damage',
                skills: [],
                stats: {
                    max_hero_stats: '140.1%',
                    exclusive_gear: '',
                    widget_bonus: ''
                },
                tier_ranking: null
            },
            {
                name: 'Patrick',
                generation: 'epic',
                type: 'Lancer',
                rarity: 'epic',
                image_url: '/images/heroes/patrick.png',
                description: '25% Increased troop health',
                skills: [],
                stats: {
                    max_hero_stats: '140.1%',
                    exclusive_gear: '',
                    widget_bonus: ''
                },
                tier_ranking: null
            },
            {
                name: 'Seo-yoon',
                generation: 'epic',
                type: 'Marksman',
                rarity: 'epic',
                image_url: '/images/heroes/placeholder.png',
                description: '25% Increased troop attack',
                skills: [],
                stats: {
                    max_hero_stats: '140.1%',
                    exclusive_gear: '',
                    widget_bonus: ''
                },
                tier_ranking: null
            },
            {
                name: 'Sergey',
                generation: 'epic',
                type: 'Infantry',
                rarity: 'epic',
                image_url: '/images/heroes/sergey.png',
                description: '20% Damage reduction for troops',
                skills: [],
                stats: {
                    max_hero_stats: '140.1%',
                    exclusive_gear: '',
                    widget_bonus: ''
                },
                tier_ranking: null
            },
            {
                name: 'Walis Bokan',
                generation: 'epic',
                type: 'Lancer',
                rarity: 'epic',
                image_url: '/images/heroes/Walis.jpg',
                description: '20% Damage reduction for troops & 20% Attack reduction for enemy troops',
                skills: [],
                stats: {
                    max_hero_stats: '140.1%',
                    exclusive_gear: '',
                    widget_bonus: ''
                },
                tier_ranking: null
            },

            // SSR HEROES (Gold - Mythic/Legendary rarity) - Generation 1
            {
                name: 'Jeronimo',
                generation: 1,
                type: 'Infantry',
                rarity: 'legendary',
                image_url: '/images/heroes/jeronimo.png',
                description: 'VIP / Hall of Chief hero with Rally skill condition',
                skills: [],
                stats: {
                    max_hero_stats: '260.2%',
                    exclusive_gear: '62.5%',
                    widget_bonus: 'Attack 15%'
                },
                tier_ranking: null
            },
            {
                name: 'Molly',
                generation: 1,
                type: 'Lancer',
                rarity: 'legendary',
                image_url: '/images/heroes/molly.png',
                description: 'Hero Hall hero with Defender skill condition',
                skills: [],
                stats: {
                    max_hero_stats: '200.2%',
                    exclusive_gear: '50%',
                    widget_bonus: 'Lethality 15%'
                },
                tier_ranking: null
            },
            {
                name: 'Natalia',
                generation: 1,
                type: 'Infantry',
                rarity: 'legendary',
                image_url: '/images/heroes/natalia.png',
                description: 'VIP hero with Rally skill condition',
                skills: [],
                stats: {
                    max_hero_stats: '200.2%',
                    exclusive_gear: '50%',
                    widget_bonus: 'Lethality 15%'
                },
                tier_ranking: null
            },
            {
                name: 'Zinman',
                generation: 1,
                type: 'Marksman',
                rarity: 'legendary',
                image_url: '/images/heroes/zinman.png',
                description: 'Lucky wheel hero with Defender skill condition',
                skills: [],
                stats: {
                    max_hero_stats: '200.2%',
                    exclusive_gear: '50%',
                    widget_bonus: 'Attack 15%'
                },
                tier_ranking: null
            },

            // SSR HEROES - Generation 2
            {
                name: 'Alonso',
                generation: 2,
                type: 'Marksman',
                rarity: 'legendary',
                image_url: '/images/heroes/alonso.png',
                description: 'Hall of Heroes hero with Rally skill condition',
                skills: [],
                stats: {
                    max_hero_stats: '240.2%',
                    exclusive_gear: '60%',
                    widget_bonus: 'Lethality 15%'
                },
                tier_ranking: null
            },
            {
                name: 'Flint',
                generation: 2,
                type: 'Infantry',
                rarity: 'legendary',
                image_url: '/images/heroes/flint.png',
                description: 'Lucky wheel hero with Defender skill condition',
                skills: [],
                stats: {
                    max_hero_stats: '240.2%',
                    exclusive_gear: '60%',
                    widget_bonus: 'Attack 15%'
                },
                tier_ranking: null
            },
            {
                name: 'Philly',
                generation: 2,
                type: 'Lancer',
                rarity: 'legendary',
                image_url: '/images/heroes/philly.png',
                description: 'Hall of Chief hero with Defender skill condition',
                skills: [],
                stats: {
                    max_hero_stats: '240.2%',
                    exclusive_gear: '60%',
                    widget_bonus: 'Health 15%'
                },
                tier_ranking: null
            },

            // SSR HEROES - Generation 3
            {
                name: 'Greg',
                generation: 3,
                type: 'Marksman',
                rarity: 'legendary',
                image_url: '/images/heroes/greg.png',
                description: 'Hall of Chief hero with Rally skill condition',
                skills: [],
                stats: {
                    max_hero_stats: '290.2%',
                    exclusive_gear: '70%',
                    widget_bonus: 'Health 15%'
                },
                tier_ranking: null
            },
            {
                name: 'Logan',
                generation: 3,
                type: 'Infantry',
                rarity: 'legendary',
                image_url: '/images/heroes/logan.png',
                description: 'Hall of Heroes hero with Defender skill condition',
                skills: [],
                stats: {
                    max_hero_stats: '290.2%',
                    exclusive_gear: '70%',
                    widget_bonus: 'Defense 15%'
                },
                tier_ranking: null
            },
            {
                name: 'Mia',
                generation: 3,
                type: 'Lancer',
                rarity: 'legendary',
                image_url: '/images/heroes/mia.png',
                description: 'Lucky wheel hero with Rally skill condition',
                skills: [],
                stats: {
                    max_hero_stats: '290.2%',
                    exclusive_gear: '70%',
                    widget_bonus: 'Attack 15%'
                },
                tier_ranking: null
            },

            // SSR HEROES - Generation 4
            {
                name: 'Ahmose',
                generation: 4,
                type: 'Infantry',
                rarity: 'legendary',
                image_url: '/images/heroes/ahmos.png',
                description: 'Hall of Chief hero with Defender skill condition',
                skills: [],
                stats: {
                    max_hero_stats: '370.3%',
                    exclusive_gear: '92.5%',
                    widget_bonus: 'Health 15%'
                },
                tier_ranking: null
            },
            {
                name: 'Reina',
                generation: 4,
                type: 'Lancer',
                rarity: 'legendary',
                image_url: '/images/heroes/Reina.jpg',
                description: 'Hall of Heroes hero with Defender skill condition',
                skills: [],
                stats: {
                    max_hero_stats: '370.3%',
                    exclusive_gear: '92.5%',
                    widget_bonus: 'Lethality 15%'
                },
                tier_ranking: null
            },
            {
                name: 'Lynn',
                generation: 4,
                type: 'Marksman',
                rarity: 'legendary',
                image_url: '/images/heroes/Lynn.jpg',
                description: 'Lucky wheel hero with Defender skill condition',
                skills: [],
                stats: {
                    max_hero_stats: '370.3%',
                    exclusive_gear: '92.5%',
                    widget_bonus: 'Lethality 15%'
                },
                tier_ranking: null
            },

            // SSR HEROES - Generation 5
            {
                name: 'Hector',
                generation: 5,
                type: 'Infantry',
                rarity: 'legendary',
                image_url: '/images/heroes/Hector.jpg',
                description: 'Lucky wheel hero with Defender skill condition',
                skills: [],
                stats: {
                    max_hero_stats: '444.35%',
                    exclusive_gear: '111%',
                    widget_bonus: 'Attack 15%'
                },
                tier_ranking: null
            },
            {
                name: 'Norah',
                generation: 5,
                type: 'Lancer',
                rarity: 'legendary',
                image_url: '/images/heroes/Norah.jpg',
                description: 'Hall of Chief hero with Defender skill condition',
                skills: [],
                stats: {
                    max_hero_stats: '444.35%',
                    exclusive_gear: '111%',
                    widget_bonus: 'Defense 15%'
                },
                tier_ranking: null
            },
            {
                name: 'Gwen',
                generation: 5,
                type: 'Marksman',
                rarity: 'legendary',
                image_url: '/images/heroes/Gwen.jpg',
                description: 'Hall of Heroes hero with Rally skill condition',
                skills: [],
                stats: {
                    max_hero_stats: '444.35%',
                    exclusive_gear: '111%',
                    widget_bonus: 'Lethality 15%'
                },
                tier_ranking: null
            },

            // SSR HEROES - Generation 6
            {
                name: 'Wu Ming',
                generation: 6,
                type: 'Infantry',
                rarity: 'legendary',
                image_url: '/images/heroes/wu_ming.png',
                description: 'Hall of Heroes hero with Defender skill condition',
                skills: [],
                stats: {
                    max_hero_stats: '540.43%',
                    exclusive_gear: '133.5%',
                    widget_bonus: 'Defense 15%'
                },
                tier_ranking: null
            },
            {
                name: 'Renee',
                generation: 6,
                type: 'Lancer',
                rarity: 'legendary',
                image_url: '/images/heroes/renee.png',
                description: 'Lucky wheel hero with Rally skill condition',
                skills: [],
                stats: {
                    max_hero_stats: '540.43%',
                    exclusive_gear: '133.5%',
                    widget_bonus: 'Lethality 15%'
                },
                tier_ranking: null
            },
            {
                name: 'Wayne',
                generation: 6,
                type: 'Marksman',
                rarity: 'legendary',
                image_url: '/images/heroes/wayne.png',
                description: 'Hall of Chief hero with Defender skill condition',
                skills: [],
                stats: {
                    max_hero_stats: '540.43%',
                    exclusive_gear: '133.5%',
                    widget_bonus: 'Lethality 15%'
                },
                tier_ranking: null
            }
        ];

        // Insert heroes into database
        for (const hero of heroes) {
            await db.createHero(hero, 1); // Created by admin user (ID 1)
        }

        console.log('🔥 Database: Initialized with', heroes.length, 'correct Whiteout Survival heroes');
    } catch (error) {
        console.error('❄️ Error initializing heroes:', error);
    }
}

// Initialize database and start server
async function startServer() {
    try {
        console.log('🔥 Initializing database connection...');
        const dbReady = await db.initialize();

        if (!dbReady) {
            console.error('❄️ Database connection failed');
            process.exit(1);
        }

        // Clean expired sessions on startup
        await db.cleanExpiredSessions();

        // Initialize heroes with correct data
        await initializeHeroes();

        app.listen(PORT, () => {
            console.log('🔥 Server running on port', PORT);
            console.log('🔥 Database connected and ready');
            console.log('🔥 Persistent login enabled');
            console.log('🔥 Text synchronization active');
            console.log(`🔥 Access: http://localhost:${PORT}`);
            console.log(`🔥 External: http://************:${PORT}`);
        });

    } catch (error) {
        console.error('❄️ Server startup failed:', error);
        process.exit(1);
    }
}

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('🔥 Shutting down server...');
    await db.close();
    process.exit(0);
});

// Start the server
startServer();
