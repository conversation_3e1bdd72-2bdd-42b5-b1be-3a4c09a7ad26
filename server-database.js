const express = require('express');
const path = require('path');
const fs = require('fs');
const cookieParser = require('cookie-parser');
const DatabaseManager = require('./js/database');

const app = express();
const PORT = 5200;

// Initialize database
const db = new DatabaseManager();

// Middleware
app.use(express.json());
app.use(express.static('.'));
app.use(cookieParser());

// CORS
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});

// Authentication middleware
async function authenticateSession(req, res, next) {
    const sessionId = req.cookies.session_id;
    
    if (sessionId) {
        const user = await db.validateSession(sessionId);
        if (user) {
            req.user = user;
        }
    }
    
    next();
}

// Apply auth middleware to all routes
app.use(authenticateSession);

// API Routes

// Login endpoint
app.post('/api/login', async (req, res) => {
    try {
        const { username, password } = req.body;
        console.log('🔥 Login attempt:', username);
        
        const user = await db.authenticateUser(username, password);
        
        if (user) {
            const sessionId = await db.createSession(user.id);
            
            if (sessionId) {
                res.cookie('session_id', sessionId, {
                    httpOnly: true,
                    secure: false, // Set to true in production with HTTPS
                    maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
                });
                
                console.log('🔥 Login successful for:', user.username);
                res.json({
                    success: true,
                    user: {
                        username: user.username,
                        role: user.role,
                        displayName: user.display_name
                    }
                });
            } else {
                res.status(500).json({ success: false, message: 'Session creation failed' });
            }
        } else {
            console.log('❄️ Login failed for:', username);
            res.status(401).json({ success: false, message: 'Invalid credentials' });
        }
    } catch (error) {
        console.error('❄️ Login error:', error);
        res.status(500).json({ success: false, message: 'Server error' });
    }
});

// Logout endpoint
app.post('/api/logout', async (req, res) => {
    try {
        const sessionId = req.cookies.session_id;
        
        if (sessionId) {
            await db.destroySession(sessionId);
        }
        
        res.clearCookie('session_id');
        res.json({ success: true });
    } catch (error) {
        console.error('❄️ Logout error:', error);
        res.status(500).json({ success: false, message: 'Server error' });
    }
});

// Check session endpoint
app.get('/api/session', (req, res) => {
    if (req.user) {
        res.json({
            authenticated: true,
            user: {
                username: req.user.username,
                role: req.user.role,
                displayName: req.user.display_name
            }
        });
    } else {
        res.json({ authenticated: false });
    }
});

// Get editable texts
app.get('/api/texts', async (req, res) => {
    try {
        const texts = await db.getEditableTexts();
        res.json(texts);
    } catch (error) {
        console.error('❄️ Error loading texts:', error);
        res.status(500).json({ error: 'Failed to load texts' });
    }
});

// Update editable text (admin only)
app.post('/api/texts', async (req, res) => {
    try {
        if (!req.user || (req.user.role !== 'admin' && req.user.role !== 'moderator')) {
            return res.status(403).json({ error: 'Unauthorized' });
        }
        
        const { fieldKey, fieldValue } = req.body;
        const success = await db.updateEditableText(fieldKey, fieldValue, req.user.user_id);
        
        if (success) {
            console.log('🔥 Text updated:', fieldKey, 'by', req.user.username);
            res.json({ success: true });
        } else {
            res.status(500).json({ error: 'Failed to update text' });
        }
    } catch (error) {
        console.error('❄️ Error updating text:', error);
        res.status(500).json({ error: 'Server error' });
    }
});

// Legacy data endpoint (for backward compatibility)
app.get('/api/data', async (req, res) => {
    try {
        // Load from JSON file for now, will migrate to database later
        const dataPath = path.join(__dirname, 'dashboard-data.json');
        
        if (fs.existsSync(dataPath)) {
            const data = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
            
            // Add editable texts from database
            const texts = await db.getEditableTexts();
            data.editableTexts = texts;
            
            res.json(data);
        } else {
            res.status(404).json({ error: 'Data file not found' });
        }
    } catch (error) {
        console.error('❄️ Error loading data:', error);
        res.status(500).json({ error: 'Failed to load data' });
    }
});

// Save data endpoint (for backward compatibility)
app.post('/api/data', (req, res) => {
    try {
        const dataPath = path.join(__dirname, 'dashboard-data.json');
        const data = req.body;
        
        // Remove editable texts from JSON (they're in database now)
        delete data.editableTexts;
        
        fs.writeFileSync(dataPath, JSON.stringify(data, null, 2));
        console.log('🔥 Data saved to JSON file');
        res.json({ success: true });
    } catch (error) {
        console.error('❄️ Error saving data:', error);
        res.status(500).json({ error: 'Failed to save data' });
    }
});

// User management endpoints (admin only)
app.get('/api/users', async (req, res) => {
    try {
        if (!req.user || req.user.role !== 'admin') {
            return res.status(403).json({ error: 'Admin access required' });
        }

        const users = await db.getAllUsers();
        res.json(users);
    } catch (error) {
        console.error('❄️ Error loading users:', error);
        res.status(500).json({ error: 'Failed to load users' });
    }
});

// Update user role (admin only)
app.post('/api/users/:userId/role', async (req, res) => {
    try {
        if (!req.user || req.user.role !== 'admin') {
            return res.status(403).json({ error: 'Admin access required' });
        }

        const { userId } = req.params;
        const { role } = req.body;

        const success = await db.updateUserRole(userId, role);

        if (success) {
            console.log('🔥 User role updated:', userId, 'to', role);
            res.json({ success: true });
        } else {
            res.status(500).json({ error: 'Failed to update user role' });
        }
    } catch (error) {
        console.error('❄️ Error updating user role:', error);
        res.status(500).json({ error: 'Server error' });
    }
});

// Create new user (admin only)
app.post('/api/users', async (req, res) => {
    try {
        if (!req.user || req.user.role !== 'admin') {
            return res.status(403).json({ error: 'Admin access required' });
        }

        const { username, password, displayName, role } = req.body;

        const userId = await db.createUser(username, password, displayName, role);

        if (userId) {
            console.log('🔥 User created:', username);
            res.json({ success: true, userId });
        } else {
            res.status(500).json({ error: 'Failed to create user' });
        }
    } catch (error) {
        console.error('❄️ Error creating user:', error);
        res.status(500).json({ error: 'Server error' });
    }
});

// Heroes API endpoints
app.get('/api/heroes', async (req, res) => {
    try {
        const heroes = await db.getAllHeroes();
        res.json(heroes);
    } catch (error) {
        console.error('❄️ Error loading heroes:', error);
        res.status(500).json({ error: 'Failed to load heroes' });
    }
});

app.post('/api/heroes', async (req, res) => {
    try {
        if (!req.user || (req.user.role !== 'admin' && req.user.role !== 'moderator')) {
            return res.status(403).json({ error: 'Unauthorized' });
        }

        const heroData = req.body;
        const heroId = await db.createHero(heroData, req.user.id);

        if (heroId) {
            console.log('🔥 Hero created:', heroData.name);
            res.json({ success: true, heroId });
        } else {
            res.status(500).json({ error: 'Failed to create hero' });
        }
    } catch (error) {
        console.error('❄️ Error creating hero:', error);
        res.status(500).json({ error: 'Server error' });
    }
});

// Error handling
app.use((error, req, res, next) => {
    console.error('❄️ Server error:', error);
    res.status(500).json({ error: 'Internal server error' });
});

// Initialize database with correct Whiteout Survival heroes
async function initializeHeroes() {
    try {
        console.log('🔥 Initializing database with correct Whiteout Survival heroes...');

        // Clear existing heroes first
        await db.pool.execute('DELETE FROM heroes');
        console.log('🔥 Database: Cleared existing heroes');

        // Correct Whiteout Survival heroes based on whiteoutdata.com
        const heroes = [
            // RARE HEROES (Blue - R rarity)
            {
                name: 'Charlie',
                generation: 'rare',
                type: 'Infantry',
                rarity: 'rare',
                image_url: '/images/heroes/placeholder.png',
                description: 'Gathering specialist with strong defensive capabilities',
                skills: ['Gathering Boost', 'Defense Formation', 'Resource Protection'],
                stats: { attack: 450, defense: 650, health: 1100 },
                tier_ranking: 3
            },
            {
                name: 'Cloris',
                generation: 'rare',
                type: 'Marksman',
                rarity: 'rare',
                image_url: '/images/heroes/placeholder.png',
                description: 'Skilled archer focused on gathering operations',
                skills: ['Precision Gathering', 'Range Boost', 'Quick Collection'],
                stats: { attack: 520, defense: 480, health: 950 },
                tier_ranking: 3
            },
            {
                name: 'Eugene',
                generation: 'rare',
                type: 'Lancer',
                rarity: 'rare',
                image_url: '/images/heroes/placeholder.png',
                description: 'Mobile gathering expert with speed bonuses',
                skills: ['Speed Gathering', 'Quick Strike', 'Mobility Boost'],
                stats: { attack: 480, defense: 520, health: 1000 },
                tier_ranking: 3
            },
            {
                name: 'Smith',
                generation: 'rare',
                type: 'Infantry',
                rarity: 'rare',
                image_url: '/images/heroes/placeholder.png',
                description: 'Crafting and gathering specialist',
                skills: ['Crafting Boost', 'Resource Efficiency', 'Tool Mastery'],
                stats: { attack: 460, defense: 680, health: 1150 },
                tier_ranking: 3
            },

            // EPIC HEROES (Purple - SR rarity)
            {
                name: 'Bahiti',
                generation: 'epic',
                type: 'Infantry',
                rarity: 'epic',
                image_url: '/images/heroes/bahiti.png',
                description: 'Desert warrior with powerful combat abilities',
                skills: ['Desert Storm', 'Sand Shield', 'Warrior\'s Fury'],
                stats: { attack: 750, defense: 680, health: 1300 },
                tier_ranking: 2
            },
            {
                name: 'Gina',
                generation: 'epic',
                type: 'Lancer',
                rarity: 'epic',
                image_url: '/images/heroes/gina.png',
                description: 'Agile lancer with wind-based attacks',
                skills: ['Wind Slash', 'Tornado Strike', 'Swift Movement'],
                stats: { attack: 720, defense: 650, health: 1250 },
                tier_ranking: 2
            },
            {
                name: 'Jasser',
                generation: 'epic',
                type: 'Marksman',
                rarity: 'epic',
                image_url: '/images/heroes/jasser.jpg',
                description: 'Elite marksman with precision shooting',
                skills: ['Precision Shot', 'Eagle Eye', 'Critical Strike'],
                stats: { attack: 780, defense: 580, health: 1150 },
                tier_ranking: 2
            },
            {
                name: 'Ling Xue',
                generation: 'epic',
                type: 'Marksman',
                rarity: 'epic',
                image_url: '/images/heroes/placeholder.png',
                description: 'Ice archer with frost-based abilities',
                skills: ['Frost Arrow', 'Ice Trap', 'Frozen Aim'],
                stats: { attack: 760, defense: 600, health: 1180 },
                tier_ranking: 2
            },
            {
                name: 'Patrick',
                generation: 'epic',
                type: 'Infantry',
                rarity: 'epic',
                image_url: '/images/heroes/patrick.png',
                description: 'Sturdy infantry with defensive focus',
                skills: ['Shield Wall', 'Defensive Stance', 'Armor Boost'],
                stats: { attack: 680, defense: 750, health: 1400 },
                tier_ranking: 2
            },
            {
                name: 'Seo-yoon',
                generation: 'epic',
                type: 'Lancer',
                rarity: 'epic',
                image_url: '/images/heroes/placeholder.png',
                description: 'Swift lancer with tactical abilities',
                skills: ['Tactical Strike', 'Formation Attack', 'Speed Boost'],
                stats: { attack: 740, defense: 670, health: 1280 },
                tier_ranking: 2
            },
            {
                name: 'Sergey',
                generation: 'epic',
                type: 'Infantry',
                rarity: 'epic',
                image_url: '/images/heroes/sergey.png',
                description: 'Battle-hardened veteran with strong leadership',
                skills: ['Battle Fury', 'Leadership', 'Veteran\'s Wisdom'],
                stats: { attack: 720, defense: 720, health: 1350 },
                tier_ranking: 2
            },
            {
                name: 'Walis Bokan',
                generation: 'epic',
                type: 'Marksman',
                rarity: 'epic',
                image_url: '/images/heroes/walis.jpg',
                description: 'Expert hunter with tracking abilities',
                skills: ['Hunter\'s Mark', 'Tracking Shot', 'Stealth'],
                stats: { attack: 770, defense: 590, health: 1160 },
                tier_ranking: 2
            },

            // SSR HEROES (Gold - Mythic/Legendary rarity) - Generation 1
            {
                name: 'Jeronimo',
                generation: 1,
                type: 'Infantry',
                rarity: 'legendary',
                image_url: '/images/heroes/jeronimo.png',
                description: 'Legendary warrior with unmatched strength',
                skills: ['Legendary Strike', 'Immortal Defense', 'Warrior\'s Pride'],
                stats: { attack: 950, defense: 850, health: 1600 },
                tier_ranking: 1
            },
            {
                name: 'Molly',
                generation: 1,
                type: 'Infantry',
                rarity: 'legendary',
                image_url: '/images/heroes/molly.png',
                description: 'Brave leader with protective abilities',
                skills: ['Shield Mastery', 'Protective Aura', 'Rally Troops'],
                stats: { attack: 880, defense: 920, health: 1650 },
                tier_ranking: 2
            },
            {
                name: 'Natalia',
                generation: 1,
                type: 'Lancer',
                rarity: 'legendary',
                image_url: '/images/heroes/natalia.png',
                description: 'Swift lancer with deadly precision',
                skills: ['Lightning Thrust', 'Wind Dance', 'Piercing Strike'],
                stats: { attack: 920, defense: 780, health: 1500 },
                tier_ranking: 2
            },
            {
                name: 'Zinman',
                generation: 1,
                type: 'Marksman',
                rarity: 'legendary',
                image_url: '/images/heroes/zinman.png',
                description: 'Master archer with divine precision',
                skills: ['Divine Arrow', 'Perfect Aim', 'Celestial Shot'],
                stats: { attack: 1000, defense: 720, health: 1400 },
                tier_ranking: 1
            },

            // SSR HEROES - Generation 2
            {
                name: 'Alonso',
                generation: 2,
                type: 'Lancer',
                rarity: 'legendary',
                image_url: '/images/heroes/alonso.png',
                description: 'Master lancer with flame abilities',
                skills: ['Flame Spear', 'Fire Charge', 'Inferno Strike'],
                stats: { attack: 940, defense: 800, health: 1520 },
                tier_ranking: 2
            },
            {
                name: 'Flint',
                generation: 2,
                type: 'Marksman',
                rarity: 'legendary',
                image_url: '/images/heroes/flint.png',
                description: 'Expert marksman with explosive arrows',
                skills: ['Explosive Arrow', 'Precision Shot', 'Eagle Eye'],
                stats: { attack: 1020, defense: 740, health: 1420 },
                tier_ranking: 2
            },
            {
                name: 'Philly',
                generation: 2,
                type: 'Infantry',
                rarity: 'legendary',
                image_url: '/images/heroes/philly.png',
                description: 'Tactical commander with strategic abilities',
                skills: ['Tactical Command', 'Strategic Strike', 'Formation Master'],
                stats: { attack: 900, defense: 940, health: 1680 },
                tier_ranking: 2
            },

            // SSR HEROES - Generation 3
            {
                name: 'Greg',
                generation: 3,
                type: 'Infantry',
                rarity: 'legendary',
                image_url: '/images/heroes/greg.png',
                description: 'Powerful warrior with crushing attacks',
                skills: ['Crushing Blow', 'Berserker Rage', 'Unstoppable Force'],
                stats: { attack: 980, defense: 880, health: 1650 },
                tier_ranking: 1
            },
            {
                name: 'Logan',
                generation: 3,
                type: 'Lancer',
                rarity: 'legendary',
                image_url: '/images/heroes/logan.png',
                description: 'Veteran lancer with combat experience',
                skills: ['Combat Veteran', 'Spear Mastery', 'Battle Hardened'],
                stats: { attack: 960, defense: 820, health: 1580 },
                tier_ranking: 2
            },
            {
                name: 'Mia',
                generation: 3,
                type: 'Marksman',
                rarity: 'legendary',
                image_url: '/images/heroes/mia.png',
                description: 'Elite sniper with stealth capabilities',
                skills: ['Stealth Shot', 'Silent Strike', 'Invisible Hunter'],
                stats: { attack: 1040, defense: 760, health: 1450 },
                tier_ranking: 2
            }
        ];

        // Insert heroes into database
        for (const hero of heroes) {
            await db.createHero(hero, 1); // Created by admin user (ID 1)
        }

        console.log('🔥 Database: Initialized with', heroes.length, 'correct Whiteout Survival heroes');
    } catch (error) {
        console.error('❄️ Error initializing heroes:', error);
    }
}

// Initialize database and start server
async function startServer() {
    try {
        console.log('🔥 Initializing database connection...');
        const dbReady = await db.initialize();

        if (!dbReady) {
            console.error('❄️ Database connection failed');
            process.exit(1);
        }

        // Clean expired sessions on startup
        await db.cleanExpiredSessions();

        // Initialize heroes with correct data
        await initializeHeroes();

        app.listen(PORT, () => {
            console.log('🔥 Server running on port', PORT);
            console.log('🔥 Database connected and ready');
            console.log('🔥 Persistent login enabled');
            console.log('🔥 Text synchronization active');
            console.log(`🔥 Access: http://localhost:${PORT}`);
            console.log(`🔥 External: http://************:${PORT}`);
        });

    } catch (error) {
        console.error('❄️ Server startup failed:', error);
        process.exit(1);
    }
}

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('🔥 Shutting down server...');
    await db.close();
    process.exit(0);
});

// Start the server
startServer();
