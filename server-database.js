const express = require('express');
const path = require('path');
const fs = require('fs');
const cookieParser = require('cookie-parser');
const DatabaseManager = require('./js/database');

const app = express();
const PORT = 5200;

// Initialize database
const db = new DatabaseManager();

// Middleware
app.use(express.json());
app.use(express.static('.'));
app.use(cookieParser());

// CORS
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});

// Authentication middleware
async function authenticateSession(req, res, next) {
    const sessionId = req.cookies.session_id;
    
    if (sessionId) {
        const user = await db.validateSession(sessionId);
        if (user) {
            req.user = user;
        }
    }
    
    next();
}

// Apply auth middleware to all routes
app.use(authenticateSession);

// API Routes

// Login endpoint
app.post('/api/login', async (req, res) => {
    try {
        const { username, password } = req.body;
        console.log('🔥 Login attempt:', username);
        
        const user = await db.authenticateUser(username, password);
        
        if (user) {
            const sessionId = await db.createSession(user.id);
            
            if (sessionId) {
                res.cookie('session_id', sessionId, {
                    httpOnly: true,
                    secure: false, // Set to true in production with HTTPS
                    maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
                });
                
                console.log('🔥 Login successful for:', user.username);
                res.json({
                    success: true,
                    user: {
                        username: user.username,
                        role: user.role,
                        displayName: user.display_name
                    }
                });
            } else {
                res.status(500).json({ success: false, message: 'Session creation failed' });
            }
        } else {
            console.log('❄️ Login failed for:', username);
            res.status(401).json({ success: false, message: 'Invalid credentials' });
        }
    } catch (error) {
        console.error('❄️ Login error:', error);
        res.status(500).json({ success: false, message: 'Server error' });
    }
});

// Logout endpoint
app.post('/api/logout', async (req, res) => {
    try {
        const sessionId = req.cookies.session_id;
        
        if (sessionId) {
            await db.destroySession(sessionId);
        }
        
        res.clearCookie('session_id');
        res.json({ success: true });
    } catch (error) {
        console.error('❄️ Logout error:', error);
        res.status(500).json({ success: false, message: 'Server error' });
    }
});

// Check session endpoint
app.get('/api/session', (req, res) => {
    if (req.user) {
        res.json({
            authenticated: true,
            user: {
                username: req.user.username,
                role: req.user.role,
                displayName: req.user.display_name
            }
        });
    } else {
        res.json({ authenticated: false });
    }
});

// Get editable texts
app.get('/api/texts', async (req, res) => {
    try {
        const texts = await db.getEditableTexts();
        res.json(texts);
    } catch (error) {
        console.error('❄️ Error loading texts:', error);
        res.status(500).json({ error: 'Failed to load texts' });
    }
});

// Update editable text (admin only)
app.post('/api/texts', async (req, res) => {
    try {
        if (!req.user || (req.user.role !== 'admin' && req.user.role !== 'moderator')) {
            return res.status(403).json({ error: 'Unauthorized' });
        }
        
        const { fieldKey, fieldValue } = req.body;
        const success = await db.updateEditableText(fieldKey, fieldValue, req.user.user_id);
        
        if (success) {
            console.log('🔥 Text updated:', fieldKey, 'by', req.user.username);
            res.json({ success: true });
        } else {
            res.status(500).json({ error: 'Failed to update text' });
        }
    } catch (error) {
        console.error('❄️ Error updating text:', error);
        res.status(500).json({ error: 'Server error' });
    }
});

// Legacy data endpoint (for backward compatibility)
app.get('/api/data', async (req, res) => {
    try {
        // Load from JSON file for now, will migrate to database later
        const dataPath = path.join(__dirname, 'dashboard-data.json');
        
        if (fs.existsSync(dataPath)) {
            const data = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
            
            // Add editable texts from database
            const texts = await db.getEditableTexts();
            data.editableTexts = texts;
            
            res.json(data);
        } else {
            res.status(404).json({ error: 'Data file not found' });
        }
    } catch (error) {
        console.error('❄️ Error loading data:', error);
        res.status(500).json({ error: 'Failed to load data' });
    }
});

// Save data endpoint (for backward compatibility)
app.post('/api/data', (req, res) => {
    try {
        const dataPath = path.join(__dirname, 'dashboard-data.json');
        const data = req.body;
        
        // Remove editable texts from JSON (they're in database now)
        delete data.editableTexts;
        
        fs.writeFileSync(dataPath, JSON.stringify(data, null, 2));
        console.log('🔥 Data saved to JSON file');
        res.json({ success: true });
    } catch (error) {
        console.error('❄️ Error saving data:', error);
        res.status(500).json({ error: 'Failed to save data' });
    }
});

// User management endpoints (admin only)
app.get('/api/users', async (req, res) => {
    try {
        if (!req.user || req.user.role !== 'admin') {
            return res.status(403).json({ error: 'Admin access required' });
        }

        const users = await db.getAllUsers();
        res.json(users);
    } catch (error) {
        console.error('❄️ Error loading users:', error);
        res.status(500).json({ error: 'Failed to load users' });
    }
});

// Update user role (admin only)
app.post('/api/users/:userId/role', async (req, res) => {
    try {
        if (!req.user || req.user.role !== 'admin') {
            return res.status(403).json({ error: 'Admin access required' });
        }

        const { userId } = req.params;
        const { role } = req.body;

        const success = await db.updateUserRole(userId, role);

        if (success) {
            console.log('🔥 User role updated:', userId, 'to', role);
            res.json({ success: true });
        } else {
            res.status(500).json({ error: 'Failed to update user role' });
        }
    } catch (error) {
        console.error('❄️ Error updating user role:', error);
        res.status(500).json({ error: 'Server error' });
    }
});

// Create new user (admin only)
app.post('/api/users', async (req, res) => {
    try {
        if (!req.user || req.user.role !== 'admin') {
            return res.status(403).json({ error: 'Admin access required' });
        }

        const { username, password, displayName, role } = req.body;

        const userId = await db.createUser(username, password, displayName, role);

        if (userId) {
            console.log('🔥 User created:', username);
            res.json({ success: true, userId });
        } else {
            res.status(500).json({ error: 'Failed to create user' });
        }
    } catch (error) {
        console.error('❄️ Error creating user:', error);
        res.status(500).json({ error: 'Server error' });
    }
});

// Heroes API endpoints
app.get('/api/heroes', async (req, res) => {
    try {
        const heroes = await db.getAllHeroes();
        res.json(heroes);
    } catch (error) {
        console.error('❄️ Error loading heroes:', error);
        res.status(500).json({ error: 'Failed to load heroes' });
    }
});

app.post('/api/heroes', async (req, res) => {
    try {
        if (!req.user || (req.user.role !== 'admin' && req.user.role !== 'moderator')) {
            return res.status(403).json({ error: 'Unauthorized' });
        }

        const heroData = req.body;
        const heroId = await db.createHero(heroData, req.user.id);

        if (heroId) {
            console.log('🔥 Hero created:', heroData.name);
            res.json({ success: true, heroId });
        } else {
            res.status(500).json({ error: 'Failed to create hero' });
        }
    } catch (error) {
        console.error('❄️ Error creating hero:', error);
        res.status(500).json({ error: 'Server error' });
    }
});

// Error handling
app.use((error, req, res, next) => {
    console.error('❄️ Server error:', error);
    res.status(500).json({ error: 'Internal server error' });
});

// Initialize database and start server
async function startServer() {
    try {
        console.log('🔥 Initializing database connection...');
        const dbReady = await db.initialize();
        
        if (!dbReady) {
            console.error('❄️ Database connection failed');
            process.exit(1);
        }
        
        // Clean expired sessions on startup
        await db.cleanExpiredSessions();
        
        app.listen(PORT, () => {
            console.log('🔥 Server running on port', PORT);
            console.log('🔥 Database connected and ready');
            console.log('🔥 Persistent login enabled');
            console.log('🔥 Text synchronization active');
            console.log(`🔥 Access: http://localhost:${PORT}`);
            console.log(`🔥 External: http://************:${PORT}`);
        });
        
    } catch (error) {
        console.error('❄️ Server startup failed:', error);
        process.exit(1);
    }
}

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('🔥 Shutting down server...');
    await db.close();
    process.exit(0);
});

// Start the server
startServer();
