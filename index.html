<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="
        default-src 'self' http: https:;
        script-src 'self' 'unsafe-inline' 'unsafe-eval' http: https: cdn.tailwindcss.com cdn.jsdelivr.net;
        script-src-attr 'unsafe-inline';
        style-src 'self' 'unsafe-inline' http: https: cdn.tailwindcss.com cdnjs.cloudflare.com fonts.googleapis.com;
        font-src 'self' http: https: fonts.gstatic.com;
        connect-src 'self' http: https:;
        img-src 'self' data: http: https:;
    ">
    <title>Whiteout Survival - State 1661 Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/rpg-awesome@0.2.0/css/rpg-awesome.min.css">

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Fire Nation x Whiteout Survival Theme */

        :root {
            --fire-red: #dc2626;
            --fire-orange: #ea580c;
            --fire-yellow: #f59e0b;
            --fire-gold: #d97706;
            --ice-blue: #0ea5e9;
            --ice-cyan: #06b6d4;
            --ice-white: #f0f9ff;
            --dark-ember: #1f2937;
            --ash-gray: #374151;
            --smoke-gray: #6b7280;
        }

        body {
            background: linear-gradient(135deg,
                #0f172a 0%,
                #1e293b 25%,
                #374151 50%,
                #1f2937 75%,
                #111827 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
            position: relative;
        }

        /* Simplified fire accent overlay - no animation on mobile */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 20% 80%, rgba(220, 38, 38, 0.08) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(234, 88, 12, 0.06) 0%, transparent 50%);
            pointer-events: none;
            z-index: 0;
        }

        /* Only animate on larger screens */
        @media (min-width: 768px) {
            body {
                background-size: 300% 300%;
                animation: subtleGradient 30s ease infinite;
            }
        }

        @keyframes subtleGradient {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .fire-glass {
            background: rgba(31, 41, 55, 0.9);
            border: 1px solid rgba(245, 158, 11, 0.3);
            position: relative;
            z-index: 1;
        }

        /* Only add expensive effects on desktop */
        @media (min-width: 768px) {
            .fire-glass {
                background: linear-gradient(135deg,
                    rgba(31, 41, 55, 0.85) 0%,
                    rgba(55, 65, 81, 0.8) 50%,
                    rgba(17, 24, 39, 0.9) 100%);
                backdrop-filter: blur(10px);
                box-shadow:
                    0 0 15px rgba(0, 0, 0, 0.2),
                    inset 0 1px 0 rgba(245, 158, 11, 0.2);
            }

            .fire-glass:hover {
                border-color: rgba(245, 158, 11, 0.5);
                box-shadow:
                    0 0 20px rgba(0, 0, 0, 0.3),
                    inset 0 1px 0 rgba(245, 158, 11, 0.3);
            }
        }
        
        .hero-card {
            background: rgba(31, 41, 55, 0.9);
            border: 2px solid rgba(245, 158, 11, 0.3);
            transition: transform 0.2s ease, border-color 0.2s ease;
            cursor: grab;
            position: relative;
            z-index: 1;
        }

        .hero-card:hover {
            transform: translateY(-2px);
            border-color: rgba(245, 158, 11, 0.5);
        }

        .hero-card.dragging {
            opacity: 0.7;
            cursor: grabbing;
            transform: scale(1.02);
        }

        /* Enhanced effects only on desktop */
        @media (min-width: 768px) {
            .hero-card {
                background: linear-gradient(145deg,
                    rgba(31, 41, 55, 0.9) 0%,
                    rgba(55, 65, 81, 0.85) 50%,
                    rgba(17, 24, 39, 0.95) 100%);
                transition: all 0.3s ease;
                overflow: hidden;
            }

            .hero-card::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg,
                    transparent,
                    rgba(245, 158, 11, 0.3),
                    transparent);
                transition: left 0.5s;
            }

            .hero-card:hover::before {
                left: 100%;
            }

            .hero-card:hover {
                transform: translateY(-5px) scale(1.01);
                border-color: rgba(245, 158, 11, 0.6);
                box-shadow:
                    0 10px 25px rgba(0, 0, 0, 0.3),
                    0 0 15px rgba(245, 158, 11, 0.2);
            }

            .hero-card.dragging {
                transform: rotate(3deg) scale(1.03);
            }
        }
        
        .particles-effect {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .snowflake {
            position: absolute;
            color: rgba(240, 249, 255, 0.6);
            user-select: none;
            pointer-events: none;
            animation: fall linear infinite;
            text-shadow: 0 0 10px rgba(14, 165, 233, 0.5);
        }

        .fire-ember {
            position: absolute;
            user-select: none;
            pointer-events: none;
            animation: rise linear infinite;
        }

        .fire-ember::before {
            content: '🔥';
            font-size: inherit;
            filter: hue-rotate(0deg);
            animation: flicker 0.5s ease-in-out infinite alternate;
        }

        @keyframes fall {
            0% {
                transform: translateY(-100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(100vh) rotate(360deg);
                opacity: 0;
            }
        }

        @keyframes rise {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100vh) rotate(180deg);
                opacity: 0;
            }
        }

        @keyframes flicker {
            0% {
                filter: hue-rotate(0deg) brightness(1);
            }
            100% {
                filter: hue-rotate(20deg) brightness(1.2);
            }
        }
        
        .mobile-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 50;
        }
        
        @media (min-width: 768px) {
            .mobile-nav {
                display: none;
            }
        }

        /* Performance optimizations */
        * {
            -webkit-tap-highlight-color: transparent;
        }

        .fire-glass, .hero-card {
            will-change: transform;
        }

        /* Reduce motion for users who prefer it */
        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Container numbering system */
        .container-numbered {
            position: relative;
        }

        .container-number {
            position: absolute;
            top: -8px;
            left: -8px;
            background: linear-gradient(45deg, #dc2626, #ea580c);
            color: white;
            font-size: 12px;
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 12px;
            z-index: 100;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            border: 2px solid #f59e0b;
            font-family: 'Inter', sans-serif;
            display: none; /* Hidden by default */
        }

        .container-number.visible {
            display: block;
        }

        .container-toggle-btn {
            position: fixed;
            top: 80px;
            right: 20px;
            z-index: 200;
            background: linear-gradient(45deg, #dc2626, #ea580c);
            color: white;
            border: 2px solid #f59e0b;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            display: none; /* Only visible to admins */
        }

        .container-toggle-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
        }

        .container-toggle-btn.visible {
            display: none; /* Hidden - now in hamburger menu */
        }





        /* Editable text styling */
        .editable-text {
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .editable-text:hover {
            background: rgba(245, 158, 11, 0.1);
            border-radius: 4px;
            padding: 2px 4px;
        }

        .editable-text.editing {
            background: rgba(245, 158, 11, 0.2);
            border-radius: 4px;
            padding: 2px 4px;
        }

        .edit-indicator {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #f59e0b;
            color: #1f2937;
            font-size: 10px;
            padding: 2px 4px;
            border-radius: 4px;
            font-weight: bold;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .editable-text:hover .edit-indicator {
            opacity: 1;
        }

        /* Role badge styles with glow effects */
        .role-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .role-pending {
            background: linear-gradient(45deg, #fbbf24, #f59e0b);
            color: #92400e;
            box-shadow: 0 0 15px rgba(251, 191, 36, 0.4);
            border-color: #fbbf24;
        }

        .role-alliance_member {
            background: linear-gradient(45deg, #10b981, #059669);
            color: white;
            box-shadow: 0 0 15px rgba(16, 185, 129, 0.4);
            border-color: #10b981;
        }

        .role-alliance_organiser {
            background: linear-gradient(45deg, #3b82f6, #2563eb);
            color: white;
            box-shadow: 0 0 15px rgba(59, 130, 246, 0.4);
            border-color: #3b82f6;
        }

        .role-moderator {
            background: linear-gradient(45deg, #8b5cf6, #7c3aed);
            color: white;
            box-shadow: 0 0 15px rgba(139, 92, 246, 0.4);
            border-color: #8b5cf6;
        }

        .role-admin {
            background: linear-gradient(45deg, #ef4444, #dc2626);
            color: white;
            box-shadow: 0 0 20px rgba(239, 68, 68, 0.5);
            border-color: #ef4444;
            animation: admin-glow 2s ease-in-out infinite alternate;
        }

        @keyframes admin-glow {
            from {
                box-shadow: 0 0 20px rgba(239, 68, 68, 0.5);
            }
            to {
                box-shadow: 0 0 30px rgba(239, 68, 68, 0.8);
            }
        }

        .role-badge:hover {
            transform: translateY(-2px);
            filter: brightness(1.1);
        }

        /* Generation filter styles */
        .generation-filter {
            padding: 8px 16px;
            border-radius: 20px;
            background: linear-gradient(45deg, #374151, #4b5563);
            color: #d1d5db;
            border: 2px solid transparent;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .generation-filter:hover {
            background: linear-gradient(45deg, #4b5563, #6b7280);
            border-color: #f59e0b;
            transform: translateY(-2px);
        }

        .generation-filter.active {
            background: linear-gradient(45deg, #f59e0b, #ef4444);
            color: white;
            border-color: #fbbf24;
            box-shadow: 0 0 15px rgba(245, 158, 11, 0.4);
        }

        /* Hero card styles */
        .hero-card {
            transition: all 0.3s ease;
        }

        .hero-card:hover {
            box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
        }

        .skill-badge {
            transition: all 0.2s ease;
        }

        .skill-badge:hover {
            transform: scale(1.05);
            filter: brightness(1.1);
        }

        /* Event Type Selector */
        .event-category {
            margin-bottom: 20px;
            display: block !important;
            visibility: visible !important;
        }

        .event-category-title {
            font-size: 16px;
            font-weight: bold;
            color: #f59e0b !important;
            margin-bottom: 12px;
            text-align: center;
            display: block !important;
            visibility: visible !important;
        }

        .event-type-btn {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            padding: 12px 16px;
            border-radius: 6px;
            border: 2px solid rgba(245, 158, 11, 0.5);
            background: rgba(55, 65, 81, 0.9);
            color: white !important;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 14px;
            font-weight: 600;
            width: 100%;
            margin-bottom: 10px;
            position: relative;
            z-index: 10;
        }

        .event-type-btn:hover {
            border-color: rgba(245, 158, 11, 0.6);
            background: rgba(55, 65, 81, 1);
            transform: translateY(-1px);
        }

        .event-type-btn.active {
            border-color: #f59e0b;
            background: linear-gradient(45deg, #dc2626, #ea580c);
            box-shadow: 0 0 10px rgba(245, 158, 11, 0.4);
        }

        .event-type-btn i {
            margin-right: 8px;
            font-size: 16px;
            color: #f59e0b;
            display: inline-block;
        }

        .event-type-btn.active i {
            color: white;
        }

        /* Mobile specific */
        @media (max-width: 767px) {
            .event-type-btn {
                padding: 12px 16px;
                font-size: 14px;
                margin-bottom: 10px;
                display: block !important;
                visibility: visible !important;
            }

            .event-type-btn i {
                font-size: 18px;
                margin-right: 10px;
                display: inline-block !important;
            }

            .event-category-title {
                font-size: 16px;
                margin-bottom: 12px;
                display: block !important;
                visibility: visible !important;
            }

            .event-category {
                display: block !important;
                visibility: visible !important;
            }

            /* Force mobile tier system visibility */
            .tier-section {
                display: block !important;
                visibility: visible !important;
                margin-bottom: 20px;
            }

            .tier-label {
                display: block !important;
                visibility: visible !important;
                margin-bottom: 10px;
            }

            .hero-slot {
                display: flex !important;
                visibility: visible !important;
                width: 60px;
                height: 60px;
                margin: 0 auto 10px auto;
            }

            /* Mobile tier row layout */
            .tier-row {
                display: block !important;
                margin-bottom: 15px;
            }

            .tier-row .flex {
                display: block !important;
            }

            /* Mobile lineup layout */
            .tier-row .flex.gap-4 {
                display: grid !important;
                grid-template-columns: repeat(3, 1fr);
                gap: 10px;
                margin-top: 10px;
            }
        }

        /* Desktop layout */
        @media (min-width: 768px) {
            .event-type-btn {
                padding: 10px 14px;
                font-size: 13px;
            }

            .event-type-btn i {
                font-size: 16px;
            }
        }

        /* Hero Tier System */
        .tier-legend {
            padding: 8px 12px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 12px;
        }

        .tier-s-plus { background: linear-gradient(45deg, #dc2626, #ef4444); color: white; }
        .tier-s { background: linear-gradient(45deg, #ea580c, #f97316); color: white; }
        .tier-a-plus { background: linear-gradient(45deg, #f59e0b, #fbbf24); color: black; }
        .tier-a { background: linear-gradient(45deg, #eab308, #facc15); color: black; }
        .tier-b-plus { background: linear-gradient(45deg, #22c55e, #4ade80); color: black; }
        .tier-b { background: linear-gradient(45deg, #10b981, #34d399); color: black; }
        .tier-c { background: linear-gradient(45deg, #06b6d4, #22d3ee); color: black; }
        .tier-dont-use { background: linear-gradient(45deg, #6b7280, #9ca3af); color: white; }

        .tier-row {
            background: rgba(31, 41, 55, 0.8);
            border: 1px solid rgba(245, 158, 11, 0.2);
            border-radius: 12px;
            padding: 16px;
            transition: all 0.3s ease;
        }

        .tier-row:hover {
            border-color: rgba(245, 158, 11, 0.4);
            background: rgba(31, 41, 55, 0.9);
        }

        /* Tier System Styling */
        .tier-section {
            display: block !important;
            visibility: visible !important;
            margin-bottom: 24px;
            opacity: 1 !important;
        }

        .tier-label {
            padding: 12px 16px;
            border-radius: 8px;
            font-weight: bold;
            font-size: 16px;
            min-width: 80px;
            text-align: center;
            color: white;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            display: block !important;
            visibility: visible !important;
        }

        .hero-slot {
            width: 80px;
            height: 80px;
            border: 2px dashed rgba(245, 158, 11, 0.5);
            border-radius: 12px;
            display: flex !important;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(55, 65, 81, 0.8);
            position: relative;
            visibility: visible !important;
            opacity: 1 !important;
        }

        .hero-slot:hover {
            border-color: rgba(245, 158, 11, 0.8);
            background: rgba(55, 65, 81, 1);
            transform: translateY(-2px);
        }

        .hero-slot.filled {
            border: 2px solid rgba(245, 158, 11, 0.8);
            background: rgba(31, 41, 55, 0.9);
        }

        .hero-slot.filled:hover {
            border-color: #f59e0b;
            box-shadow: 0 0 15px rgba(245, 158, 11, 0.3);
        }

        .hero-image {
            width: 70px;
            height: 70px;
            border-radius: 8px;
            object-fit: cover;
        }

        .hero-type-icon {
            position: absolute;
            bottom: -8px;
            right: -8px;
            background: linear-gradient(45deg, #dc2626, #ea580c);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            border: 2px solid #1f2937;
        }

        .hero-name {
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 10px;
            color: #f59e0b;
            font-weight: bold;
            white-space: nowrap;
            max-width: 80px;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .hero-placeholder {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg,
                rgba(55, 65, 81, 0.8),
                rgba(31, 41, 55, 0.9),
                rgba(17, 24, 39, 0.95));
            border: 2px dashed rgba(245, 158, 11, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            position: relative;
            overflow: hidden;
        }

        /* Only add pulse effect on desktop */
        @media (min-width: 768px) {
            .hero-placeholder::before {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                width: 100%;
                height: 100%;
                background: radial-gradient(circle,
                    rgba(245, 158, 11, 0.15) 0%,
                    transparent 70%);
                transform: translate(-50%, -50%);
                animation: pulse 3s ease-in-out infinite;
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: translate(-50%, -50%) scale(1);
                opacity: 0.4;
            }
            50% {
                transform: translate(-50%, -50%) scale(1.1);
                opacity: 0.7;
            }
        }

        .fire-nation-title {
            font-family: 'Cinzel', serif;
            color: #f59e0b !important;
            background: none !important;
            -webkit-text-fill-color: #f59e0b !important;
            text-shadow:
                0 0 8px rgba(245, 158, 11, 0.8),
                0 0 16px rgba(220, 38, 38, 0.6),
                0 0 24px rgba(234, 88, 12, 0.4);
        }

        /* Enhanced glow effect on desktop */
        @media (min-width: 768px) {
            .fire-nation-title {
                text-shadow:
                    0 0 10px rgba(245, 158, 11, 0.9),
                    0 0 20px rgba(220, 38, 38, 0.7),
                    0 0 30px rgba(234, 88, 12, 0.5),
                    0 0 40px rgba(14, 165, 233, 0.3);
                animation: fireTextGlow 4s ease-in-out infinite;
            }
        }



        @keyframes fireTextGlow {
            0%, 100% {
                text-shadow:
                    0 0 10px rgba(245, 158, 11, 0.9),
                    0 0 20px rgba(220, 38, 38, 0.7),
                    0 0 30px rgba(234, 88, 12, 0.5);
            }
            50% {
                text-shadow:
                    0 0 15px rgba(245, 158, 11, 1),
                    0 0 25px rgba(220, 38, 38, 0.8),
                    0 0 35px rgba(234, 88, 12, 0.6),
                    0 0 45px rgba(14, 165, 233, 0.4);
            }
        }

        .fire-icon {
            color: #f59e0b;
        }

        /* Only animate on desktop */
        @media (min-width: 768px) {
            .fire-icon {
                filter: drop-shadow(0 0 8px rgba(245, 158, 11, 0.4));
                animation: iconFlicker 3s ease-in-out infinite;
            }
        }

        @keyframes iconFlicker {
            0%, 100% {
                filter: drop-shadow(0 0 8px rgba(245, 158, 11, 0.4));
            }
            50% {
                filter: drop-shadow(0 0 12px rgba(220, 38, 38, 0.6));
            }
        }
    </style>
</head>
<body class="text-white">
    <!-- Fire & Ice Particles Effect -->
    <div class="particles-effect" id="particlesContainer"></div>

    <!-- Container Toggle Button (Admin Only) -->
    <button id="containerToggleBtn" class="container-toggle-btn">
        <i class="fas fa-hashtag mr-1"></i>
        Show #IDs
    </button>




    
    <!-- Header -->
    <header class="fire-glass sticky top-0 z-40 p-4 container-numbered">
        <div class="container-number">#001</div>
        <div class="container mx-auto flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <i class="fas fa-fire fire-icon text-2xl"></i>
                <h1 class="text-xl md:text-2xl font-bold fire-nation-title">FNA - State 1661</h1>
            </div>
            
            <!-- Desktop Navigation -->
            <nav class="hidden md:flex space-x-6">
                <a href="#heroes" class="hover:text-yellow-300 transition-colors group">
                    <i class="fas fa-users text-orange-400 mr-2 group-hover:text-yellow-300"></i>Heroes
                </a>
                <a href="#events" class="hover:text-yellow-300 transition-colors group">
                    <i class="fas fa-calendar text-orange-400 mr-2 group-hover:text-yellow-300"></i>Events
                </a>
                <a href="#stats" class="hover:text-yellow-300 transition-colors group">
                    <i class="fas fa-chart-bar text-orange-400 mr-2 group-hover:text-yellow-300"></i>Stats
                </a>
                <a href="#heroes" class="hover:text-yellow-300 transition-colors group">
                    <i class="fas fa-users text-orange-400 mr-2 group-hover:text-yellow-300"></i>Heroes
                </a>
                <a href="#admin" id="adminNavLink" class="hidden hover:text-yellow-300 transition-colors group admin-only">
                    <i class="fas fa-cog text-orange-400 mr-2 group-hover:text-yellow-300"></i>Admin
                </a>
                <a href="#users" id="usersNavLink" class="hidden hover:text-yellow-300 transition-colors group admin-only">
                    <i class="fas fa-users text-orange-400 mr-2 group-hover:text-yellow-300"></i>Users
                </a>
                <a href="#profile" id="profileNavLink" class="hidden hover:text-yellow-300 transition-colors group">
                    <i class="fas fa-user text-orange-400 mr-2 group-hover:text-yellow-300"></i>Profile
                </a>
                <button id="toggleContainersBtn" class="hidden hover:text-yellow-300 transition-colors group admin-only">
                    <i class="fas fa-hashtag text-orange-400 mr-2 group-hover:text-yellow-300"></i>
                    <span id="toggleContainersText">Show Container IDs</span>
                </button>
            </nav>
            
            <!-- User Menu -->
            <div class="flex items-center space-x-3">
                <!-- Discord Button -->
                <button id="joinDiscordBtn" class="bg-blue-600 hover:bg-blue-700 px-3 py-2 rounded-lg transition-colors text-sm">
                    <i class="fab fa-discord mr-1"></i>Discord
                </button>

                <button id="loginBtn" class="hover:text-yellow-300 transition-colors group">
                    <i class="fas fa-sign-in-alt text-orange-400 mr-2 group-hover:text-yellow-300"></i>Login
                </button>
                <div id="userMenu" class="hidden">
                    <span id="userName" class="mr-3"></span>
                    <button id="logoutBtn" class="bg-red-600 hover:bg-red-700 px-3 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sign-out-alt text-gray-400"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8 pb-20 md:pb-8">
        <!-- Welcome Section -->
        <section class="fire-glass rounded-xl p-6 mb-8 container-numbered">
            <div class="container-number">#002</div>
            <div class="flex flex-col md:flex-row items-center gap-6">
                <!-- Left Image -->
                <div class="w-24 h-24 md:w-32 md:h-32 flex-shrink-0">
                    <img src="images/events/FNA-Alliance.png" alt="FNA Alliance" class="w-full h-full object-contain">
                </div>

                <!-- Center Content -->
                <div class="flex-1 text-center">
                    <h2 class="text-2xl md:text-3xl font-bold mb-4 fire-nation-title">
                        <i class="fas fa-fire fire-icon mr-3"></i>
                        <span class="editable-text" data-field="welcome-title">Welcome to FNA - Fire Nation</span>
                    </h2>
                    <p class="text-orange-100 text-lg">
                        <span class="editable-text" data-field="welcome-subtitle">🔥 Dominating State 1661 with Fire & Ice ❄️</span>
                    </p>
                    <p class="text-yellow-200 text-sm mt-2">
                        <span class="editable-text" data-field="welcome-quote">"Fire is the element of power" - Avatar Universe</span>
                    </p>
                </div>

                <!-- Right Image -->
                <div class="w-24 h-24 md:w-32 md:h-32 flex-shrink-0">
                    <img src="images/events/SunfireFNA.png" alt="Sunfire FNA" class="w-full h-full object-contain">
                </div>
            </div>
        </section>

        <!-- Hero Rankings Section -->
        <section id="heroes" class="fire-glass rounded-xl p-6 mb-8 container-numbered">
            <div class="container-number">#003</div>
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl md:text-2xl font-bold fire-nation-title">
                    <i class="fas fa-crown fire-icon mr-3"></i>
                    <span class="editable-text" data-field="heroes-title">Hero Tier System</span>
                </h3>
                <div class="flex space-x-2">
                    <button id="manageHeroesBtn" class="hidden bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 px-4 py-2 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl">
                        <i class="fas fa-cog text-yellow-200 mr-2"></i>Manage Heroes
                    </button>
                    <button id="editTiersBtn" class="hidden bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700 px-4 py-2 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl">
                        <i class="fas fa-edit text-yellow-200 mr-2"></i>Edit Tiers
                    </button>
                </div>
            </div>

            <!-- Event Type Selector -->
            <div class="mb-6 p-4 bg-gray-800 bg-opacity-50 rounded-lg">
                <h4 class="font-bold mb-3 text-orange-200">Select Event Type</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4" id="eventTypeSelector">
                    <!-- Event type categories will be generated here -->
                </div>
            </div>

            <!-- Tier Legend -->
            <div class="mb-6 p-4 bg-gray-800 bg-opacity-50 rounded-lg">
                <h4 class="font-bold mb-3 text-orange-200">
                    <span id="currentEventTitle">Formation Guide: Infantry + Lancer + Marksman</span>
                </h4>
                <div class="grid grid-cols-4 md:grid-cols-8 gap-2 text-center text-sm">
                    <div class="tier-legend tier-s-plus">S+</div>
                    <div class="tier-legend tier-s">S</div>
                    <div class="tier-legend tier-a-plus">A+</div>
                    <div class="tier-legend tier-a">A</div>
                    <div class="tier-legend tier-b-plus">B+</div>
                    <div class="tier-legend tier-b">B</div>
                    <div class="tier-legend tier-c">C</div>
                    <div class="tier-legend tier-dont-use">Don't Use</div>
                </div>
                <div class="mt-3 text-sm text-blue-200" id="eventDescription">
                    <!-- Event specific description will be shown here -->
                </div>
            </div>

            <!-- Tier System -->
            <div id="heroTierSystem" class="space-y-6">
                <!-- Tiers will be dynamically generated -->
            </div>
        </section>

        <!-- Events Section -->
        <section id="events" class="fire-glass rounded-xl p-6 mb-8 container-numbered">
            <div class="container-number">#004</div>
            <h3 class="text-xl md:text-2xl font-bold mb-6 fire-nation-title">
                <i class="fas fa-calendar-alt fire-icon mr-3"></i>
                <span class="editable-text" data-field="events-title">Current Events</span>
            </h3>
            <!-- Add Event Button (Admin Only) -->
            <div class="mb-4 admin-only hidden">
                <button id="addEventBtn" class="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 px-4 py-2 rounded-lg transition-all duration-300 font-medium">
                    <i class="fas fa-plus mr-2"></i>Add Event
                </button>
            </div>

            <!-- Events Grid -->
            <div id="eventsGrid" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="bg-blue-800 bg-opacity-50 rounded-lg p-4">
                    <h4 class="font-bold text-lg mb-2">Alliance War</h4>
                    <p class="text-blue-200">Prepare your best heroes for the upcoming alliance war!</p>
                    <div class="mt-3 text-sm text-blue-300">
                        <i class="fas fa-clock text-gray-400 mr-2"></i>
                        Starts in 2 days
                    </div>
                </div>
                <div class="bg-purple-800 bg-opacity-50 rounded-lg p-4">
                    <h4 class="font-bold text-lg mb-2">Hero Recruitment</h4>
                    <p class="text-purple-200">Special recruitment event with increased rates!</p>
                    <div class="mt-3 text-sm text-purple-300">
                        <i class="fas fa-clock text-gray-400 mr-2"></i>
                        3 days remaining
                    </div>
                </div>
            </div>
        </section>

        <!-- Stats Section -->
        <section id="stats" class="fire-glass rounded-xl p-6 mb-8 container-numbered">
            <div class="container-number">#005</div>
            <h3 class="text-xl md:text-2xl font-bold mb-6 fire-nation-title">
                <i class="fas fa-chart-line fire-icon mr-3"></i>
                <span class="editable-text" data-field="stats-title">Alliance Statistics</span>
            </h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center">
                    <div class="text-2xl md:text-3xl font-bold text-blue-300">
                        <span class="editable-text" data-field="stat1-value">1,247</span>
                    </div>
                    <div class="text-sm text-blue-200">
                        <span class="editable-text" data-field="stat1-label">Active Players</span>
                    </div>
                </div>
                <div class="text-center">
                    <div class="text-2xl md:text-3xl font-bold text-green-300">
                        <span class="editable-text" data-field="stat2-value">89</span>
                    </div>
                    <div class="text-sm text-green-200">
                        <span class="editable-text" data-field="stat2-label">Alliances</span>
                    </div>
                </div>
                <div class="text-center">
                    <div class="text-2xl md:text-3xl font-bold text-yellow-300">
                        <span class="editable-text" data-field="stat3-value">156M</span>
                    </div>
                    <div class="text-sm text-yellow-200">
                        <span class="editable-text" data-field="stat3-label">Total Power</span>
                    </div>
                </div>
                <div class="text-center">
                    <div class="text-2xl md:text-3xl font-bold text-red-300">
                        <span class="editable-text" data-field="stat4-value">42</span>
                    </div>
                    <div class="text-sm text-red-200">
                        <span class="editable-text" data-field="stat4-label">Days Active</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Bear Trap #1 Section -->
        <section id="bear-trap-1" class="fire-glass rounded-xl p-6 mb-8 container-numbered">
            <div class="container-number">#008</div>
            <h3 class="text-xl md:text-2xl font-bold mb-6 fire-nation-title">
                <i class="fas fa-chart-line fire-icon mr-3"></i>
                <span class="editable-text" data-field="bear1-title">Bear Trap #1</span>
            </h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center">
                    <div class="text-2xl md:text-3xl font-bold text-blue-300">
                        <span class="editable-text" data-field="bear1-stat1-value">Level 45</span>
                    </div>
                    <div class="text-sm text-blue-200">
                        <span class="editable-text" data-field="bear1-stat1-label">Bear Level</span>
                    </div>
                </div>
                <div class="text-center">
                    <div class="text-2xl md:text-3xl font-bold text-green-300">
                        <span class="editable-text" data-field="bear1-stat2-value">2.5M</span>
                    </div>
                    <div class="text-sm text-green-200">
                        <span class="editable-text" data-field="bear1-stat2-label">Bear HP</span>
                    </div>
                </div>
                <div class="text-center">
                    <div class="text-2xl md:text-3xl font-bold text-yellow-300">
                        <span class="editable-text" data-field="bear1-stat3-value">15:30</span>
                    </div>
                    <div class="text-sm text-yellow-200">
                        <span class="editable-text" data-field="bear1-stat3-label">Spawn Time</span>
                    </div>
                </div>
                <div class="text-center">
                    <div class="text-2xl md:text-3xl font-bold text-red-300">
                        <span class="editable-text" data-field="bear1-stat4-value">Active</span>
                    </div>
                    <div class="text-sm text-red-200">
                        <span class="editable-text" data-field="bear1-stat4-label">Status</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Bear Trap #2 Section -->
        <section id="bear-trap-2" class="fire-glass rounded-xl p-6 mb-8 container-numbered">
            <div class="container-number">#009</div>
            <h3 class="text-xl md:text-2xl font-bold mb-6 fire-nation-title">
                <i class="fas fa-chart-line fire-icon mr-3"></i>
                <span class="editable-text" data-field="bear2-title">Bear Trap #2</span>
            </h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center">
                    <div class="text-2xl md:text-3xl font-bold text-blue-300">
                        <span class="editable-text" data-field="bear2-stat1-value">Level 50</span>
                    </div>
                    <div class="text-sm text-blue-200">
                        <span class="editable-text" data-field="bear2-stat1-label">Bear Level</span>
                    </div>
                </div>
                <div class="text-center">
                    <div class="text-2xl md:text-3xl font-bold text-green-300">
                        <span class="editable-text" data-field="bear2-stat2-value">3.2M</span>
                    </div>
                    <div class="text-sm text-green-200">
                        <span class="editable-text" data-field="bear2-stat2-label">Bear HP</span>
                    </div>
                </div>
                <div class="text-center">
                    <div class="text-2xl md:text-3xl font-bold text-yellow-300">
                        <span class="editable-text" data-field="bear2-stat3-value">18:45</span>
                    </div>
                    <div class="text-sm text-yellow-200">
                        <span class="editable-text" data-field="bear2-stat3-label">Spawn Time</span>
                    </div>
                </div>
                <div class="text-center">
                    <div class="text-2xl md:text-3xl font-bold text-red-300">
                        <span class="editable-text" data-field="bear2-stat4-value">Pending</span>
                    </div>
                    <div class="text-sm text-red-200">
                        <span class="editable-text" data-field="bear2-stat4-label">Status</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Heroes Page -->
        <section id="heroes" class="hidden">
            <div class="fire-glass rounded-xl p-6 mb-8 container-numbered">
                <div class="container-number">#006</div>
                <h2 class="text-2xl md:text-3xl font-bold mb-6 fire-nation-title">
                    <i class="fas fa-users fire-icon mr-3"></i>
                    Heroes Database
                </h2>

                <!-- Generation Filters -->
                <div class="mb-6 flex flex-wrap gap-3">
                    <button class="generation-filter active" data-generation="all">
                        All Generations
                    </button>
                    <button class="generation-filter" data-generation="1">
                        Generation 1
                    </button>
                    <button class="generation-filter" data-generation="2">
                        Generation 2
                    </button>
                    <button class="generation-filter" data-generation="3">
                        Generation 3
                    </button>
                    <button class="generation-filter" data-generation="4">
                        Generation 4
                    </button>
                </div>

                <!-- Heroes by Generation -->
                <div id="heroesGenerations">
                    <!-- Will be populated by JavaScript -->
                </div>
            </div>
        </section>

        <!-- Admin Section (Only visible to admins) -->
        <section id="admin" class="fire-glass rounded-xl p-6 hidden container-numbered">
            <div class="container-number">#010</div>
            <h3 class="text-xl md:text-2xl font-bold mb-6 fire-nation-title">
                <i class="fas fa-crown fire-icon mr-3"></i>
                Fire Nation Command Center
            </h3>

            <!-- User Management -->
            <div class="mb-8">
                <div class="flex justify-between items-center mb-4">
                    <h4 class="text-lg font-bold text-orange-200">User Management</h4>
                    <button id="createUserBtn" class="bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700 px-4 py-2 rounded-lg transition-all duration-300">
                        <i class="fas fa-user-plus text-yellow-200 mr-2"></i>Create User
                    </button>
                </div>
                <div id="usersList" class="space-y-2">
                    <!-- Users will be loaded here -->
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button id="uploadHeroesBtn" class="fire-glass p-4 rounded-lg hover:border-yellow-400 transition-colors text-left">
                    <i class="fas fa-upload text-orange-400 mb-2 text-xl"></i>
                    <div class="font-bold">Upload Heroes</div>
                    <div class="text-sm text-gray-400">Add hero images & data</div>
                </button>
                <button id="createEventBtn" class="fire-glass p-4 rounded-lg hover:border-yellow-400 transition-colors text-left">
                    <i class="fas fa-calendar-plus text-green-400 mb-2 text-xl"></i>
                    <div class="font-bold">Create Event</div>
                    <div class="text-sm text-gray-400">Add new game events</div>
                </button>
                <button id="backupDataBtn" class="fire-glass p-4 rounded-lg hover:border-yellow-400 transition-colors text-left">
                    <i class="fas fa-database text-blue-400 mb-2 text-xl"></i>
                    <div class="font-bold">Backup Data</div>
                    <div class="text-sm text-gray-400">Export dashboard data</div>
                </button>
            </div>
        </section>
    </main>

    <!-- Footer with User Management -->
    <footer class="fire-glass mt-8 p-6">
        <div class="container mx-auto">
            <!-- Online Users (Visible to All) -->
            <div class="mb-6 p-4 bg-gray-800 bg-opacity-50 rounded-lg">
                <h4 class="font-bold mb-3 text-orange-200">
                    <i class="fas fa-circle text-green-400 mr-2"></i>Online Users
                </h4>
                <div id="footerOnlineUsers" class="flex flex-wrap gap-3">
                    <!-- Online users will be loaded here -->
                    <span class="text-sm text-gray-400">Loading online users...</span>
                </div>
            </div>

            <!-- User Management Section (Admin Only) -->
            <div id="userManagementFooter" class="hidden admin-only mb-6">
                <h3 class="text-xl md:text-2xl font-bold mb-6 fire-nation-title">
                    <i class="fas fa-users fire-icon mr-3"></i>
                    User Management
                </h3>

                <!-- User Roles Legend -->
                <div class="mb-6 p-4 bg-gray-800 bg-opacity-50 rounded-lg">
                    <h4 class="font-bold mb-3 text-orange-200">User Roles</h4>
                    <div class="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                        <div>
                            <span class="font-semibold text-yellow-400">Pending:</span>
                            <span class="text-gray-300">Awaiting approval</span>
                        </div>
                        <div>
                            <span class="font-semibold text-green-400">Alliance Member:</span>
                            <span class="text-gray-300">Basic access</span>
                        </div>
                        <div>
                            <span class="font-semibold text-blue-400">Alliance Organiser:</span>
                            <span class="text-gray-300">Event management</span>
                        </div>
                        <div>
                            <span class="font-semibold text-purple-400">Moderator:</span>
                            <span class="text-gray-300">Content moderation</span>
                        </div>
                        <div>
                            <span class="font-semibold text-red-400">Admin:</span>
                            <span class="text-gray-300">Full access</span>
                        </div>
                    </div>
                </div>

                <!-- User List -->
                <div class="space-y-2" id="footerUsersList">
                    <!-- Users will be loaded here -->
                </div>
            </div>

            <!-- Discord Integration (Visible to All) -->
            <!-- Discord section removed - moved to header -->

            <!-- Footer Info -->
            <div class="text-center text-gray-400 text-sm">
                <p>&copy; 2024 FNA - Fire Nation Alliance | State 1661</p>
                <p class="mt-2">🔥 Dominating with Fire & Ice ❄️</p>
            </div>
        </div>
    </footer>

    <!-- Mobile Navigation -->
    <nav class="mobile-nav fire-glass p-4 container-numbered">
        <div class="container-number">#007</div>
        <div class="flex justify-around" id="mobileNavItems">
            <a href="#heroes" class="flex flex-col items-center text-orange-200 hover:text-yellow-300 transition-colors">
                <i class="fas fa-crown text-xl mb-1"></i>
                <span class="text-xs">Heroes</span>
            </a>
            <a href="#events" class="flex flex-col items-center text-orange-200 hover:text-yellow-300 transition-colors">
                <i class="fas fa-calendar text-xl mb-1"></i>
                <span class="text-xs">Events</span>
            </a>
            <a href="#stats" class="flex flex-col items-center text-orange-200 hover:text-yellow-300 transition-colors">
                <i class="fas fa-chart-bar text-xl mb-1"></i>
                <span class="text-xs">Stats</span>
            </a>
            <a href="#bear-trap-1" class="flex flex-col items-center text-orange-200 hover:text-yellow-300 transition-colors">
                <i class="fas fa-paw text-xl mb-1"></i>
                <span class="text-xs">Bear #1</span>
            </a>
            <a href="#bear-trap-2" class="flex flex-col items-center text-orange-200 hover:text-yellow-300 transition-colors">
                <i class="fas fa-paw text-xl mb-1"></i>
                <span class="text-xs">Bear #2</span>
            </a>
        </div>
    </nav>

    <!-- Hero Management Modal -->
    <div id="heroManagementModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
        <div class="fire-glass rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold fire-nation-title">
                        <i class="fas fa-users fire-icon mr-2"></i>
                        Hero Management
                    </h3>
                    <button id="closeHeroModal" class="text-gray-400 hover:text-white">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <!-- Add New Hero Section -->
                <div class="mb-8 p-4 bg-gray-800 bg-opacity-50 rounded-lg">
                    <h4 class="font-bold mb-4 text-orange-200">Add New Hero</h4>
                    <form id="addHeroForm" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-orange-200 mb-2">Hero Name</label>
                            <input type="text" id="heroName" required class="w-full px-3 py-2 bg-gray-800 border border-orange-400 rounded-lg text-white focus:outline-none focus:border-yellow-400">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-orange-200 mb-2">Hero Type</label>
                            <select id="heroType" required class="w-full px-3 py-2 bg-gray-800 border border-orange-400 rounded-lg text-white focus:outline-none focus:border-yellow-400">
                                <option value="">Select Type</option>
                                <option value="infantry">Infantry</option>
                                <option value="lancer">Lancer</option>
                                <option value="marksman">Marksman</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-orange-200 mb-2">Profile Image</label>
                            <input type="file" id="heroImage" accept="image/*" class="w-full px-3 py-2 bg-gray-800 border border-orange-400 rounded-lg text-white focus:outline-none focus:border-yellow-400">
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-orange-200 mb-2">Expedition Skills (3 required)</label>
                            <div id="expeditionSkills" class="space-y-3">
                                <div class="skill-input-group grid grid-cols-1 md:grid-cols-2 gap-2">
                                    <input type="text" placeholder="Skill 1 Name" class="skill-name bg-gray-800 border border-orange-400 rounded-lg text-white px-3 py-2 focus:outline-none focus:border-yellow-400">
                                    <input type="text" placeholder="Skill 1 Description" class="skill-desc bg-gray-800 border border-orange-400 rounded-lg text-white px-3 py-2 focus:outline-none focus:border-yellow-400">
                                </div>
                                <div class="skill-input-group grid grid-cols-1 md:grid-cols-2 gap-2">
                                    <input type="text" placeholder="Skill 2 Name" class="skill-name bg-gray-800 border border-orange-400 rounded-lg text-white px-3 py-2 focus:outline-none focus:border-yellow-400">
                                    <input type="text" placeholder="Skill 2 Description" class="skill-desc bg-gray-800 border border-orange-400 rounded-lg text-white px-3 py-2 focus:outline-none focus:border-yellow-400">
                                </div>
                                <div class="skill-input-group grid grid-cols-1 md:grid-cols-2 gap-2">
                                    <input type="text" placeholder="Skill 3 Name" class="skill-name bg-gray-800 border border-orange-400 rounded-lg text-white px-3 py-2 focus:outline-none focus:border-yellow-400">
                                    <input type="text" placeholder="Skill 3 Description" class="skill-desc bg-gray-800 border border-orange-400 rounded-lg text-white px-3 py-2 focus:outline-none focus:border-yellow-400">
                                </div>
                            </div>
                        </div>
                        <div class="md:col-span-2">
                            <button type="submit" class="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 px-6 py-2 rounded-lg transition-all duration-300 font-medium">
                                <i class="fas fa-plus mr-2"></i>Add Hero
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Existing Heroes -->
                <div>
                    <h4 class="font-bold mb-4 text-orange-200">Existing Heroes</h4>
                    <div id="existingHeroesList" class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                        <!-- Heroes will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Hero Selection Modal -->
    <div id="heroSelectionModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
        <div class="fire-glass rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold fire-nation-title">
                        <i class="fas fa-hand-pointer fire-icon mr-2"></i>
                        Select Hero
                    </h3>
                    <button id="closeSelectionModal" class="text-gray-400 hover:text-white">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <!-- Filter by Type -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-orange-200 mb-2">Filter by Type</label>
                    <select id="heroTypeFilter" class="w-full px-3 py-2 bg-gray-800 border border-orange-400 rounded-lg text-white focus:outline-none focus:border-yellow-400">
                        <option value="">All Types</option>
                        <option value="infantry">Infantry</option>
                        <option value="lancer">Lancer</option>
                        <option value="marksman">Marksman</option>
                    </select>
                </div>

                <!-- Hero Selection Grid -->
                <div id="heroSelectionGrid" class="grid grid-cols-3 md:grid-cols-4 gap-4">
                    <!-- Heroes will be loaded here -->
                </div>
            </div>
        </div>
    </div>



    <!-- Login Modal -->
    <div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4 hidden">
        <div class="fire-glass rounded-xl max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-bold fire-nation-title">
                    <i class="fas fa-sign-in-alt fire-icon mr-2"></i>
                    Login to FNA Dashboard
                </h3>
                <button id="closeLoginModal" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <form id="loginForm" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-orange-200 mb-2">Username</label>
                    <input type="text" id="username" required
                           class="w-full px-3 py-2 bg-gray-800 border border-orange-400 rounded-lg text-white focus:outline-none focus:border-yellow-400"
                           placeholder="Enter username">
                </div>

                <div>
                    <label class="block text-sm font-medium text-orange-200 mb-2">Password</label>
                    <input type="password" id="password" required
                           class="w-full px-3 py-2 bg-gray-800 border border-orange-400 rounded-lg text-white focus:outline-none focus:border-yellow-400"
                           placeholder="Enter password">
                </div>

                <div class="flex gap-3">
                    <button type="submit" class="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 px-4 py-2 rounded-lg transition-all duration-300 font-medium">
                        <i class="fas fa-sign-in-alt mr-2"></i>Login
                    </button>
                    <button type="button" id="cancelLogin" class="bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded-lg transition-colors font-medium">
                        Cancel
                    </button>
                </div>
            </form>

            <div class="mt-4 text-sm text-gray-400 text-center">
                <p>Default admin credentials: admin / fna2024</p>
            </div>
        </div>
    </div>

    <!-- Force HTTP script loading -->
    <!-- Vervang dynamische loader door statische script-tags -->
    <script src="/js/app.js"></script>
    <script src="/js/auth.js"></script>
    <script src="/js/heroes.js"></script>
</body>
</html>
