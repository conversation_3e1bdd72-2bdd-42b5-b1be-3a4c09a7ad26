// Authentication System for Fire Nation Dashboard

class AuthSystem {
    // The constructor now receives the 'users' part of the global AppState
    constructor(usersState) {
        this.state = usersState; // this.state is now a reference to AppState.users
        this.setupEventListeners();
        this.checkLocalhostAdmin();
        this.updateUI();
    }

    checkLocalhostAdmin() {
        console.log('🔥 Checking hostname:', window.location.hostname);
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            console.log('🔥 Localhost detected - activating auto-admin mode');
            this.state.currentUser = {
                username: 'localhost-admin',
                role: 'admin',
                displayName: 'Localhost Admin'
            };
            console.log('🔥 Auto-admin user set:', this.state.currentUser);
        }
    }

    setupEventListeners() {
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                const username = loginForm.querySelector('#username').value;
                const password = loginForm.querySelector('#password').value;
                this.login(username, password);
            });
        }
    }

    login(username, password) {
        const user = this.state.users.find(u => u.username === username && u.password === password);
        if (user) {
            this.state.currentUser = { username: user.username, role: user.role };
            saveDataToServer(); // Persist the change to the server
            window.dashboard.showNotification(`Welcome, ${user.username}! 🔥`, 'success');
            window.dashboard.hideLoginModal();
            window.dashboard.updateLoginStatus();
        } else {
            window.dashboard.showNotification('Invalid username or password! ❄️', 'error');
        }
    }

    logout() {
        this.state.currentUser = null;
        saveDataToServer(); // Persist the change to the server
        window.dashboard.showNotification('You have been logged out! ❄️', 'info');
        window.dashboard.updateLoginStatus();
    }

    getCurrentUser() {
        return this.state.currentUser;
    }

    updateUI() {
        // This method can be used to update any auth-specific UI elements if needed in the future
    }

    // Creating users will also modify the state and save it.
    createUser(username, password, role = 'viewer') {
        if (this.state.users.find(u => u.username === username)) {
            window.dashboard.showNotification('User already exists!', 'error');
            return;
        }
        this.state.users.push({ username, password, role });
        saveDataToServer();
        window.dashboard.showNotification(`User ${username} created!`, 'success');
    }
}
