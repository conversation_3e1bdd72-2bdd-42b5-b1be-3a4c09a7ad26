// Authentication System for Fire Nation Dashboard

class AuthSystem {
    // The constructor now receives the 'users' part of the global AppState
    constructor(usersState) {
        console.log('🔥 Auth: Constructor received usersState:', usersState);

        // Handle different data structures
        if (usersState && typeof usersState === 'object') {
            // If it's the server format (object with user objects as values)
            if (!usersState.users && !usersState.currentUser) {
                // Convert server format to expected format
                this.state = {
                    users: Object.values(usersState),
                    currentUser: null
                };
                console.log('🔥 Auth: Converted server format to:', this.state);
            } else {
                // It's already in the expected format
                this.state = usersState;
            }
        } else {
            // Fallback
            this.state = { users: [], currentUser: null };
        }

        this.setupEventListeners();
        this.checkLocalhostAdmin();
        this.updateUI();
    }

    checkLocalhostAdmin() {
        console.log('🔥 Checking hostname:', window.location.hostname);
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            console.log('🔥 Localhost detected - activating auto-admin mode');
            this.state.currentUser = {
                username: 'localhost-admin',
                role: 'admin',
                displayName: 'Localhost Admin'
            };
            console.log('🔥 Auto-admin user set:', this.state.currentUser);
        }
    }

    setupEventListeners() {
        console.log('🔥 Auth: Setting up event listeners...');

        // Login button in header
        const loginBtn = document.getElementById('loginBtn');
        if (loginBtn) {
            console.log('🔥 Auth: Login button found');
            loginBtn.addEventListener('click', () => {
                console.log('🔥 Auth: Login button clicked');
                this.showLoginModal();
            });
        } else {
            console.log('🔥 Auth: Login button NOT found');
        }

        // Login form
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            console.log('🔥 Auth: Login form found');
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                const username = loginForm.querySelector('#username').value;
                const password = loginForm.querySelector('#password').value;
                console.log('🔥 Auth: Login attempt for:', username);
                this.login(username, password);
            });
        } else {
            console.log('🔥 Auth: Login form NOT found');
        }

        // Logout button
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            console.log('🔥 Auth: Logout button found');
            logoutBtn.addEventListener('click', () => {
                console.log('🔥 Auth: Logout button clicked');
                this.logout();
            });
        } else {
            console.log('🔥 Auth: Logout button NOT found');
        }

        // Close modal buttons
        const closeLoginModal = document.getElementById('closeLoginModal');
        const cancelLogin = document.getElementById('cancelLogin');

        if (closeLoginModal) {
            closeLoginModal.addEventListener('click', () => this.hideLoginModal());
        }

        if (cancelLogin) {
            cancelLogin.addEventListener('click', () => this.hideLoginModal());
        }

        // Click outside modal to close
        const loginModal = document.getElementById('loginModal');
        if (loginModal) {
            loginModal.addEventListener('click', (e) => {
                if (e.target === loginModal) {
                    this.hideLoginModal();
                }
            });
        }
    }

    showLoginModal() {
        console.log('🔥 Auth: Showing login modal');
        const modal = document.getElementById('loginModal');
        if (modal) {
            modal.classList.remove('hidden');
            // Focus on username field
            setTimeout(() => {
                const usernameField = document.getElementById('username');
                if (usernameField) usernameField.focus();
            }, 100);
        }
    }

    hideLoginModal() {
        console.log('🔥 Auth: Hiding login modal');
        const modal = document.getElementById('loginModal');
        if (modal) {
            modal.classList.add('hidden');
            // Clear form
            const form = document.getElementById('loginForm');
            if (form) form.reset();
        }
    }

    login(username, password) {
        console.log('🔥 Auth: Login attempt for:', username);
        console.log('🔥 Auth: Auth state:', this.state);
        console.log('🔥 Auth: Available users:', this.state.users);

        // Find user in the users array
        const user = this.state.users.find(u => u.username === username && u.password === password);

        console.log('🔥 Auth: Found user:', user);

        if (user) {
            console.log('🔥 Auth: Login successful for:', user);
            this.state.currentUser = {
                username: user.username,
                role: user.role,
                displayName: user.displayName || user.username
            };

            this.hideLoginModal();
            this.updateUI();

            // Show success notification
            console.log('🔥 Auth: Login completed, updating UI');
            alert(`Welcome, ${user.displayName || user.username}! 🔥`);
        } else {
            console.log('🔥 Auth: Login failed - invalid credentials');
            alert('Invalid username or password! ❄️');
        }
    }

    logout() {
        console.log('🔥 Auth: Logging out user');
        this.state.currentUser = null;
        this.updateUI();
        alert('You have been logged out! ❄️');
    }

    getCurrentUser() {
        return this.state.currentUser;
    }

    updateUI() {
        console.log('🔥 Auth: Updating UI for user:', this.state.currentUser);

        const loginBtn = document.getElementById('loginBtn');
        const userMenu = document.getElementById('userMenu');
        const userName = document.getElementById('userName');
        const adminElements = document.querySelectorAll('.admin-only');

        console.log('🔥 Auth: Found UI elements:', {
            loginBtn: !!loginBtn,
            userMenu: !!userMenu,
            userName: !!userName,
            adminElements: adminElements.length
        });

        if (this.state.currentUser) {
            console.log('🔥 Auth: User is logged in, showing user menu');
            // User is logged in - hide login button, show user menu
            if (loginBtn) loginBtn.style.display = 'none';
            if (userMenu) {
                userMenu.style.display = 'flex';
                userMenu.classList.remove('hidden');
            }
            if (userName) userName.textContent = this.state.currentUser.displayName || this.state.currentUser.username;

            // Show admin elements for admin/contributor
            if (this.state.currentUser.role === 'admin' || this.state.currentUser.role === 'contributor') {
                console.log('🔥 Auth: User has admin/contributor role, showing admin elements');
                adminElements.forEach(el => {
                    el.style.display = 'block';
                    el.classList.add('visible');
                    console.log('🔥 Auth: Made admin element visible:', el.id || el.className);
                });
            }
        } else {
            console.log('🔥 Auth: No user logged in, showing login button');
            // User is not logged in - show login button, hide user menu
            if (loginBtn) loginBtn.style.display = 'block';
            if (userMenu) {
                userMenu.style.display = 'none';
                userMenu.classList.add('hidden');
            }

            // Hide admin elements
            adminElements.forEach(el => {
                el.style.display = 'none';
                el.classList.remove('visible');
            });
        }

        // Re-render hero system to show/hide admin controls
        if (window.heroManager) {
            console.log('🔥 Auth: Re-rendering hero system for new auth state');
            window.heroManager.renderTierSystem();
        }
    }

    // Creating users will also modify the state and save it.
    createUser(username, password, role = 'viewer') {
        if (this.state.users.find(u => u.username === username)) {
            window.dashboard.showNotification('User already exists!', 'error');
            return;
        }
        this.state.users.push({ username, password, role });
        saveDataToServer();
        window.dashboard.showNotification(`User ${username} created!`, 'success');
    }
}
