// Authentication System for Fire Nation Dashboard

class AuthSystem {
    // The constructor now receives the 'users' part of the global AppState
    constructor(usersState) {
        console.log('🔥 Auth: Constructor - using database authentication');

        // Initialize state
        this.state = {
            users: [],
            currentUser: null
        };

        this.setupEventListeners();
        this.checkSession(); // Check for existing session
        this.updateUI();
    }

    async checkSession() {
        try {
            console.log('🔥 Auth: Checking existing session...');
            const response = await fetch('/api/session');
            const data = await response.json();

            if (data.authenticated) {
                console.log('🔥 Auth: Found existing session for:', data.user.username);
                this.state.currentUser = data.user;
                this.updateUI();
            } else {
                console.log('🔥 Auth: No existing session found');
            }
        } catch (error) {
            console.error('❄️ Auth: Session check failed:', error);
        }
    }

    setupEventListeners() {
        console.log('🔥 Auth: Setting up event listeners...');

        // Login button in header
        const loginBtn = document.getElementById('loginBtn');
        if (loginBtn) {
            console.log('🔥 Auth: Login button found');
            loginBtn.addEventListener('click', () => {
                console.log('🔥 Auth: Login button clicked');
                this.showLoginModal();
            });
        } else {
            console.log('🔥 Auth: Login button NOT found');
        }

        // Login form
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            console.log('🔥 Auth: Login form found');
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                const username = loginForm.querySelector('#username').value;
                const password = loginForm.querySelector('#password').value;
                console.log('🔥 Auth: Login attempt for:', username);
                this.login(username, password);
            });
        } else {
            console.log('🔥 Auth: Login form NOT found');
        }

        // Logout button
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            console.log('🔥 Auth: Logout button found');
            logoutBtn.addEventListener('click', () => {
                console.log('🔥 Auth: Logout button clicked');
                this.logout();
            });
        } else {
            console.log('🔥 Auth: Logout button NOT found');
        }

        // Close modal buttons
        const closeLoginModal = document.getElementById('closeLoginModal');
        const cancelLogin = document.getElementById('cancelLogin');

        if (closeLoginModal) {
            closeLoginModal.addEventListener('click', () => this.hideLoginModal());
        }

        if (cancelLogin) {
            cancelLogin.addEventListener('click', () => this.hideLoginModal());
        }

        // Click outside modal to close
        const loginModal = document.getElementById('loginModal');
        if (loginModal) {
            loginModal.addEventListener('click', (e) => {
                if (e.target === loginModal) {
                    this.hideLoginModal();
                }
            });
        }
    }

    showLoginModal() {
        console.log('🔥 Auth: Showing login modal');
        const modal = document.getElementById('loginModal');
        if (modal) {
            modal.classList.remove('hidden');
            // Focus on username field
            setTimeout(() => {
                const usernameField = document.getElementById('username');
                if (usernameField) usernameField.focus();
            }, 100);
        }
    }

    hideLoginModal() {
        console.log('🔥 Auth: Hiding login modal');
        const modal = document.getElementById('loginModal');
        if (modal) {
            modal.classList.add('hidden');
            // Clear form
            const form = document.getElementById('loginForm');
            if (form) form.reset();
        }
    }

    async login(username, password) {
        try {
            console.log('🔥 Auth: Login attempt for:', username);

            const response = await fetch('/api/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ username, password })
            });

            const data = await response.json();

            if (data.success) {
                console.log('🔥 Auth: Login successful for:', data.user);
                this.state.currentUser = data.user;

                this.hideLoginModal();
                this.updateUI();

                // Show success notification
                console.log('🔥 Auth: Login completed, updating UI');
                alert(`Welcome, ${data.user.displayName || data.user.username}! 🔥`);
            } else {
                console.log('🔥 Auth: Login failed -', data.message);
                alert('Invalid username or password! ❄️');
            }
        } catch (error) {
            console.error('❄️ Auth: Login error:', error);
            alert('Login failed - server error! ❄️');
        }
    }

    async logout() {
        try {
            console.log('🔥 Auth: Logging out user');

            await fetch('/api/logout', {
                method: 'POST'
            });

            this.state.currentUser = null;
            this.updateUI();
            alert('You have been logged out! ❄️');
        } catch (error) {
            console.error('❄️ Auth: Logout error:', error);
            // Still clear local state even if server call fails
            this.state.currentUser = null;
            this.updateUI();
            alert('You have been logged out! ❄️');
        }
    }

    getCurrentUser() {
        return this.state.currentUser;
    }

    updateUI() {
        console.log('🔥 Auth: Updating UI for user:', this.state.currentUser);

        const loginBtn = document.getElementById('loginBtn');
        const userMenu = document.getElementById('userMenu');
        const userName = document.getElementById('userName');
        const adminElements = document.querySelectorAll('.admin-only');

        console.log('🔥 Auth: Found UI elements:', {
            loginBtn: !!loginBtn,
            userMenu: !!userMenu,
            userName: !!userName,
            adminElements: adminElements.length
        });

        if (this.state.currentUser) {
            console.log('🔥 Auth: User is logged in, showing user menu');
            // User is logged in - hide login button, show user menu
            if (loginBtn) loginBtn.style.display = 'none';
            if (userMenu) {
                userMenu.style.display = 'flex';
                userMenu.classList.remove('hidden');
            }
            if (userName) userName.textContent = this.state.currentUser.displayName || this.state.currentUser.username;

            // Show admin elements for admin/contributor
            if (this.state.currentUser.role === 'admin' || this.state.currentUser.role === 'contributor') {
                console.log('🔥 Auth: User has admin/contributor role, showing admin elements');
                adminElements.forEach(el => {
                    el.style.display = 'block';
                    el.classList.add('visible');
                    el.classList.remove('hidden');
                    console.log('🔥 Auth: Made admin element visible:', el.id || el.className);
                });

                // Also show container toggle button specifically
                const toggleContainersBtn = document.getElementById('toggleContainersBtn');
                if (toggleContainersBtn) {
                    toggleContainersBtn.style.display = 'block';
                    toggleContainersBtn.classList.remove('hidden');
                    console.log('🔥 Auth: Made container toggle button visible');
                }
            }
        } else {
            console.log('🔥 Auth: No user logged in, showing login button');
            // User is not logged in - show login button, hide user menu
            if (loginBtn) loginBtn.style.display = 'block';
            if (userMenu) {
                userMenu.style.display = 'none';
                userMenu.classList.add('hidden');
            }

            // Hide admin elements
            adminElements.forEach(el => {
                el.style.display = 'none';
                el.classList.remove('visible');
            });
        }

        // Re-render hero system to show/hide admin controls
        if (window.heroManager) {
            console.log('🔥 Auth: Re-rendering hero system for new auth state');
            window.heroManager.renderTierSystem();
        }
    }

    // Creating users will also modify the state and save it.
    createUser(username, password, role = 'viewer') {
        if (this.state.users.find(u => u.username === username)) {
            window.dashboard.showNotification('User already exists!', 'error');
            return;
        }
        this.state.users.push({ username, password, role });
        saveDataToServer();
        window.dashboard.showNotification(`User ${username} created!`, 'success');
    }
}
