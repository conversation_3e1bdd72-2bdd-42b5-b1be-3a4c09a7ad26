// Authentication System for Fire Nation Dashboard

class AuthSystem {
    // The constructor now receives the 'users' part of the global AppState
    constructor(usersState) {
        this.state = usersState; // this.state is now a reference to AppState.users
        this.setupEventListeners();
        this.checkLocalhostAdmin();
        this.updateUI();
    }

    checkLocalhostAdmin() {
        console.log('🔥 Checking hostname:', window.location.hostname);
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            console.log('🔥 Localhost detected - activating auto-admin mode');
            this.state.currentUser = {
                username: 'localhost-admin',
                role: 'admin',
                displayName: 'Localhost Admin'
            };
            console.log('🔥 Auto-admin user set:', this.state.currentUser);
        }
    }

    setupEventListeners() {
        console.log('🔥 Auth: Setting up event listeners...');

        // Login button in header
        const loginBtn = document.getElementById('loginBtn');
        if (loginBtn) {
            console.log('🔥 Auth: Login button found');
            loginBtn.addEventListener('click', () => {
                console.log('🔥 Auth: Login button clicked');
                this.showLoginModal();
            });
        } else {
            console.log('🔥 Auth: Login button NOT found');
        }

        // Login form
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            console.log('🔥 Auth: Login form found');
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                const username = loginForm.querySelector('#username').value;
                const password = loginForm.querySelector('#password').value;
                console.log('🔥 Auth: Login attempt for:', username);
                this.login(username, password);
            });
        } else {
            console.log('🔥 Auth: Login form NOT found');
        }

        // Close modal buttons
        const closeLoginModal = document.getElementById('closeLoginModal');
        const cancelLogin = document.getElementById('cancelLogin');

        if (closeLoginModal) {
            closeLoginModal.addEventListener('click', () => this.hideLoginModal());
        }

        if (cancelLogin) {
            cancelLogin.addEventListener('click', () => this.hideLoginModal());
        }

        // Click outside modal to close
        const loginModal = document.getElementById('loginModal');
        if (loginModal) {
            loginModal.addEventListener('click', (e) => {
                if (e.target === loginModal) {
                    this.hideLoginModal();
                }
            });
        }
    }

    showLoginModal() {
        console.log('🔥 Auth: Showing login modal');
        const modal = document.getElementById('loginModal');
        if (modal) {
            modal.classList.remove('hidden');
            // Focus on username field
            setTimeout(() => {
                const usernameField = document.getElementById('username');
                if (usernameField) usernameField.focus();
            }, 100);
        }
    }

    hideLoginModal() {
        console.log('🔥 Auth: Hiding login modal');
        const modal = document.getElementById('loginModal');
        if (modal) {
            modal.classList.add('hidden');
            // Clear form
            const form = document.getElementById('loginForm');
            if (form) form.reset();
        }
    }

    login(username, password) {
        console.log('🔥 Auth: Login attempt for:', username);
        console.log('🔥 Auth: Available users:', this.state.users);

        const user = this.state.users.find(u => u.username === username && u.password === password);
        if (user) {
            console.log('🔥 Auth: Login successful for:', user);
            this.state.currentUser = {
                username: user.username,
                role: user.role,
                displayName: user.displayName || user.username
            };

            this.hideLoginModal();
            this.updateUI();

            // Show success notification
            console.log('🔥 Auth: Login completed, updating UI');
            alert(`Welcome, ${user.displayName || user.username}! 🔥`);
        } else {
            console.log('🔥 Auth: Login failed - invalid credentials');
            alert('Invalid username or password! ❄️');
        }
    }

    logout() {
        this.state.currentUser = null;
        saveDataToServer(); // Persist the change to the server
        window.dashboard.showNotification('You have been logged out! ❄️', 'info');
        window.dashboard.updateLoginStatus();
    }

    getCurrentUser() {
        return this.state.currentUser;
    }

    updateUI() {
        // This method can be used to update any auth-specific UI elements if needed in the future
    }

    // Creating users will also modify the state and save it.
    createUser(username, password, role = 'viewer') {
        if (this.state.users.find(u => u.username === username)) {
            window.dashboard.showNotification('User already exists!', 'error');
            return;
        }
        this.state.users.push({ username, password, role });
        saveDataToServer();
        window.dashboard.showNotification(`User ${username} created!`, 'success');
    }
}
