// Clean Dashboard System - Maintains exact functionality with clean code
class CleanDashboard {
    constructor() {
        this.data = null;
        this.currentUser = null;
        this.currentEventType = 'bear-captain';
        this.eventTypes = [
            { id: 'bear-captain', name: 'Bear - Captain', icon: 'crown', category: 'Bear Hunt' },
            { id: 'bear-joiner', name: '<PERSON> <PERSON> Jo<PERSON>', icon: 'users', category: 'Bear Hunt' },
            { id: 'pvp-att-captain', name: 'PvP Attacker - Captain', icon: 'sword', category: 'PvP' },
            { id: 'pvp-att-joiner', name: 'PvP Attacker - Joiner', icon: 'shield', category: 'PvP' },
            { id: 'pvp-def-captain', name: 'PvP Defender - Captain', icon: 'castle', category: 'PvP' },
            { id: 'pvp-def-joiner', name: 'PvP Defender - Joiner', icon: 'tower', category: 'PvP' }
        ];
        this.tiers = ['S+', 'S', 'A+', 'A', 'B+', 'B', 'C', "Don't Use"];
        
        this.init();
    }

    async init() {
        console.log('🔥 Initializing Clean Dashboard...');

        // Load data from server first
        await this.loadData();

        // Setup UI
        this.setupEventListeners();
        this.renderEventTypeSelector();
        this.renderTierSystem();
        this.updateTexts();

        // Check for localhost auto-admin AFTER UI is setup
        this.checkLocalhostAdmin();

        console.log('🔥 Clean Dashboard initialized successfully!');
    }

    checkLocalhostAdmin() {
        console.log('🔥 Checking hostname:', window.location.hostname);
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            console.log('🔥 Localhost detected - activating auto-admin mode');
            this.currentUser = {
                username: 'localhost-admin',
                role: 'admin',
                displayName: 'Localhost Admin'
            };
            console.log('🔥 Current user set:', this.currentUser);
            this.updateAuthUI();
            console.log('🔥 Auth UI updated for auto-admin');
        } else {
            console.log('🔥 Not localhost, no auto-admin');
        }
    }

    async loadData() {
        try {
            const response = await fetch('/api/data');
            this.data = await response.json();
            console.log('🔥 Data loaded successfully');
        } catch (error) {
            console.error('🔥 Error loading data:', error);
        }
    }

    async saveData() {
        try {
            const response = await fetch('/api/data', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(this.data)
            });
            const result = await response.json();
            if (result.success) {
                console.log('🔥 Data saved successfully');
            }
        } catch (error) {
            console.error('🔥 Error saving data:', error);
        }
    }

    setupEventListeners() {
        console.log('🔥 Setting up event listeners...');

        // Login button
        const loginBtn = document.getElementById('loginBtn');
        if (loginBtn) {
            console.log('🔥 Login button found, adding event listener');
            loginBtn.addEventListener('click', () => {
                console.log('🔥 Login button clicked!');
                this.showLoginModal();
            });
        } else {
            console.log('🔥 Login button NOT found!');
        }

        // Logout button
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            console.log('🔥 Logout button found, adding event listener');
            logoutBtn.addEventListener('click', () => {
                console.log('🔥 Logout button clicked!');
                this.logout();
            });
        }

        // Manage heroes button
        const manageHeroesBtn = document.getElementById('manageHeroesBtn');
        if (manageHeroesBtn) {
            console.log('🔥 Manage heroes button found, adding event listener');
            manageHeroesBtn.addEventListener('click', () => {
                console.log('🔥 Manage heroes button clicked!');
                this.showHeroManagementModal();
            });
        }

        console.log('🔥 Event listeners setup complete');
    }

    renderEventTypeSelector() {
        const selector = document.getElementById('eventTypeSelector');
        if (!selector) return;

        selector.innerHTML = '';

        this.eventTypes.forEach(eventType => {
            const button = document.createElement('button');
            button.className = `event-type-btn ${eventType.id === this.currentEventType ? 'active' : ''}`;
            button.innerHTML = `
                <i class="fas fa-${eventType.icon}"></i>
                ${eventType.name}
            `;

            button.addEventListener('click', () => {
                this.switchEventType(eventType.id);
            });

            selector.appendChild(button);
        });
    }

    switchEventType(eventTypeId) {
        console.log('🔥 Switching to event type:', eventTypeId);
        this.currentEventType = eventTypeId;
        this.renderEventTypeSelector();
        this.renderTierSystem();
        this.showNotification(`Switched to ${this.getEventTypeName(eventTypeId)}! 🔥`, 'info');
    }

    getEventTypeName(eventTypeId) {
        const eventType = this.eventTypes.find(et => et.id === eventTypeId);
        return eventType ? eventType.name : 'Unknown Event';
    }

    renderTierSystem() {
        const container = document.getElementById('heroTierSystem');
        if (!container || !this.data) return;

        container.innerHTML = '';

        this.tiers.forEach(tier => {
            const tierLineups = this.getTierLineups(tier);
            const canEdit = this.currentUser && (this.currentUser.role === 'admin' || this.currentUser.role === 'contributor');

            // Show tiers with content, or empty tiers for editors
            if (tierLineups.length === 0 && !canEdit) {
                return;
            }

            const tierSection = this.createTierSection(tier, tierLineups, canEdit);
            container.appendChild(tierSection);
        });
    }

    getTierLineups(tier) {
        if (!this.data?.tierData?.[this.currentEventType]?.[tier]) {
            return [];
        }
        return this.data.tierData[this.currentEventType][tier];
    }

    createTierSection(tier, lineups, canEdit) {
        const section = document.createElement('div');
        section.className = 'tier-section mb-6';

        const tierClass = `tier-${tier.toLowerCase().replace('+', '-plus').replace(' ', '-').replace("'", '')}`;
        
        section.innerHTML = `
            <div class="flex items-center justify-between mb-4">
                <div class="tier-label ${tierClass}">${tier}</div>
                ${canEdit ? `<button class="add-lineup-btn bg-green-600 hover:bg-green-700 px-3 py-1 rounded text-sm" data-tier="${tier}">
                    <i class="fas fa-plus mr-1"></i>Add Lineup
                </button>` : ''}
            </div>
            <div class="tier-lineups space-y-4">
                ${lineups.map((lineup, index) => this.createLineupHTML(lineup, index, tier, canEdit)).join('')}
                ${lineups.length === 0 && canEdit ? this.createEmptyLineupHTML(0, tier) : ''}
            </div>
        `;

        // Add event listeners for admin functions
        if (canEdit) {
            section.querySelector('.add-lineup-btn')?.addEventListener('click', () => {
                this.addNewLineup(tier);
            });
        }

        return section;
    }

    createLineupHTML(lineup, lineupIndex, tier, canEdit) {
        const slots = Array(3).fill(null).map((_, slotIndex) => {
            const heroId = lineup[slotIndex];
            const hero = heroId ? this.data.heroes.find(h => h.id === heroId) : null;
            
            return `
                <div class="hero-slot ${hero ? 'filled' : ''}" 
                     data-tier="${tier}" 
                     data-lineup="${lineupIndex}" 
                     data-slot="${slotIndex}">
                    ${hero ? `
                        <img src="${hero.image || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iOCIgZmlsbD0iIzM3NDE1MSIvPgo8dGV4dCB4PSIyMCIgeT0iMjQiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxMiIgZmlsbD0iI0Y1OUUwQiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+Pz88L3RleHQ+Cjwvc3ZnPgo='}" 
                               alt="${hero.name}" class="hero-image">
                        <div class="hero-type-icon">
                            <i class="fas fa-${this.getTypeIcon(hero.type)}"></i>
                        </div>
                        <div class="hero-name">${hero.name}</div>
                    ` : `
                        <i class="fas fa-plus text-gray-400 text-xl"></i>
                    `}
                </div>
            `;
        }).join('');

        return `
            <div class="tier-row bg-gray-800 bg-opacity-50 rounded-lg p-4">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm text-gray-300">Lineup #${lineupIndex + 1}</span>
                    ${canEdit ? `<button class="delete-lineup-btn text-red-400 hover:text-red-300" data-tier="${tier}" data-lineup="${lineupIndex}">
                        <i class="fas fa-trash text-sm"></i>
                    </button>` : ''}
                </div>
                <div class="flex gap-4 justify-center">
                    ${slots}
                </div>
            </div>
        `;
    }

    createEmptyLineupHTML(lineupIndex, tier) {
        const slots = Array(3).fill(null).map((_, slotIndex) => `
            <div class="hero-slot" data-tier="${tier}" data-lineup="${lineupIndex}" data-slot="${slotIndex}">
                <i class="fas fa-plus text-gray-400 text-xl"></i>
            </div>
        `).join('');

        return `
            <div class="tier-row bg-gray-800 bg-opacity-50 rounded-lg p-4">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm text-gray-300">Lineup #${lineupIndex + 1}</span>
                </div>
                <div class="flex gap-4 justify-center">
                    ${slots}
                </div>
            </div>
        `;
    }

    getTypeIcon(type) {
        const icons = {
            infantry: 'shield-alt',
            lancer: 'sword',
            marksman: 'crosshairs'
        };
        return icons[type] || 'user';
    }

    addNewLineup(tier) {
        if (!this.data.tierData[this.currentEventType]) {
            this.data.tierData[this.currentEventType] = {};
        }
        if (!this.data.tierData[this.currentEventType][tier]) {
            this.data.tierData[this.currentEventType][tier] = [];
        }

        this.data.tierData[this.currentEventType][tier].push([]);
        this.saveData();
        this.renderTierSystem();
        this.showNotification(`Added new lineup to ${tier}! 🔥`, 'success');
    }

    updateTexts() {
        if (!this.data?.editableTexts) return;

        Object.entries(this.data.editableTexts).forEach(([key, value]) => {
            const element = document.getElementById(key);
            if (element) {
                element.textContent = value;
            }
        });
    }

    updateAuthUI() {
        console.log('🔥 Updating auth UI for user:', this.currentUser);

        const loginBtn = document.getElementById('loginBtn');
        const userMenu = document.getElementById('userMenu');
        const userName = document.getElementById('userName');
        const adminElements = document.querySelectorAll('.admin-only');

        console.log('🔥 Found elements:', {
            loginBtn: !!loginBtn,
            userMenu: !!userMenu,
            userName: !!userName,
            adminElements: adminElements.length
        });

        if (this.currentUser) {
            console.log('🔥 User is logged in, updating UI...');
            // User is logged in
            if (loginBtn) loginBtn.classList.add('hidden');
            if (userMenu) userMenu.classList.remove('hidden');
            if (userName) userName.textContent = this.currentUser.displayName;

            // Show admin elements for admin/contributor
            if (this.currentUser.role === 'admin' || this.currentUser.role === 'contributor') {
                console.log('🔥 User has admin/contributor role, showing admin elements');
                adminElements.forEach(el => {
                    el.classList.add('visible');
                    console.log('🔥 Made admin element visible:', el.id || el.className);
                });
            }
        } else {
            console.log('🔥 No user logged in, hiding admin elements');
            // User is not logged in
            if (loginBtn) loginBtn.classList.remove('hidden');
            if (userMenu) userMenu.classList.add('hidden');
            adminElements.forEach(el => el.classList.remove('visible'));
        }
    }

    showLoginModal() {
        console.log('🔥 Showing login modal...');

        // Create modal
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4';
        modal.id = 'loginModal';
        modal.innerHTML = `
            <div class="fire-glass rounded-xl max-w-md w-full p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold fire-nation-title">
                        <i class="fas fa-sign-in-alt fire-icon mr-2"></i>
                        Login to FNA Dashboard
                    </h3>
                    <button class="close-login-modal text-gray-400 hover:text-white">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <form id="loginForm" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-orange-200 mb-2">Username</label>
                        <input type="text" id="loginUsername" required
                               class="w-full px-3 py-2 bg-gray-800 border border-orange-400 rounded-lg text-white focus:outline-none focus:border-yellow-400"
                               placeholder="Enter username">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-orange-200 mb-2">Password</label>
                        <input type="password" id="loginPassword" required
                               class="w-full px-3 py-2 bg-gray-800 border border-orange-400 rounded-lg text-white focus:outline-none focus:border-yellow-400"
                               placeholder="Enter password">
                    </div>

                    <div class="flex gap-3">
                        <button type="submit" class="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 px-4 py-2 rounded-lg transition-all duration-300 font-medium">
                            <i class="fas fa-sign-in-alt mr-2"></i>Login
                        </button>
                        <button type="button" class="close-login-modal bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded-lg transition-colors font-medium">
                            Cancel
                        </button>
                    </div>
                </form>

                <div class="mt-4 text-sm text-gray-400 text-center">
                    <p>Default admin credentials: admin / fna2024</p>
                </div>
            </div>
        `;

        // Add event listeners
        modal.querySelector('.close-login-modal').addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        modal.querySelector('#loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const username = modal.querySelector('#loginUsername').value;
            const password = modal.querySelector('#loginPassword').value;

            if (username && password) {
                await this.login(username, password);
                document.body.removeChild(modal);
            }
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });

        document.body.appendChild(modal);

        // Focus on username field
        setTimeout(() => {
            modal.querySelector('#loginUsername').focus();
        }, 100);
    }

    async login(username, password) {
        try {
            const response = await fetch('/api/login', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ username, password })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.currentUser = result.user;
                this.updateAuthUI();
                this.renderTierSystem(); // Re-render to show admin controls
                this.showNotification(`Welcome back, ${this.currentUser.displayName}! 🔥`, 'success');
            } else {
                this.showNotification('Invalid credentials! 🔥', 'error');
            }
        } catch (error) {
            console.error('Login error:', error);
            this.showNotification('Login failed! 🔥', 'error');
        }
    }

    logout() {
        this.currentUser = null;
        this.updateAuthUI();
        this.renderTierSystem(); // Re-render to hide admin controls
        this.showNotification('Logged out successfully! 🔥', 'info');
    }

    showHeroManagementModal() {
        this.showNotification('Hero management coming soon! 🔥', 'info');
    }

    showNotification(message, type = 'info') {
        console.log(`🔥 [${type.toUpperCase()}]: ${message}`);
        
        // Simple notification system
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg text-white font-medium ${
            type === 'success' ? 'bg-green-600' : 
            type === 'error' ? 'bg-red-600' : 
            'bg-blue-600'
        }`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new CleanDashboard();
});
