// Create dummy users for testing user management
const mysql = require('mysql2/promise');
const bcrypt = require('bcrypt');

async function createDummyUsers() {
    let connection;
    
    try {
        console.log('🔥 Connecting to database...');
        
        connection = await mysql.createConnection({
            host: 'localhost',
            user: 'root',
            password: 'PolitieDB2025SecurePassword',
            database: 'whiteout_dashboard'
        });
        
        console.log('✅ Connected to database');
        
        // Dummy users to create
        const dummyUsers = [
            {
                username: 'john_doe',
                password: 'test123',
                displayName: '<PERSON>',
                role: 'alliance_member'
            },
            {
                username: 'jane_smith',
                password: 'test123',
                displayName: '<PERSON>',
                role: 'alliance_organiser'
            },
            {
                username: 'mike_wilson',
                password: 'test123',
                displayName: '<PERSON>',
                role: 'moderator'
            },
            {
                username: 'sarah_jones',
                password: 'test123',
                displayName: '<PERSON>',
                role: 'pending'
            }
        ];
        
        console.log('🔥 Creating dummy users...');
        
        for (const user of dummyUsers) {
            try {
                // Hash password
                const hashedPassword = await bcrypt.hash(user.password, 10);
                
                // Insert user
                await connection.execute(
                    'INSERT INTO users (username, password_hash, display_name, role) VALUES (?, ?, ?, ?)',
                    [user.username, hashedPassword, user.displayName, user.role]
                );
                
                console.log(`✅ Created user: ${user.username} (${user.role})`);
                
            } catch (error) {
                if (error.code === 'ER_DUP_ENTRY') {
                    console.log(`ℹ️  User ${user.username} already exists`);
                } else {
                    console.error(`❌ Error creating user ${user.username}:`, error.message);
                }
            }
        }
        
        // Verify users were created
        const [users] = await connection.execute('SELECT username, display_name, role FROM users ORDER BY created_at');
        console.log('\n📋 All users in database:');
        users.forEach(user => {
            console.log(`  - ${user.display_name} (@${user.username}) - ${user.role}`);
        });
        
        console.log('\n🎉 Dummy users creation complete!');
        
    } catch (error) {
        console.error('❌ Error:', error);
        process.exit(1);
    } finally {
        if (connection) {
            await connection.end();
            console.log('🔥 Database connection closed');
        }
    }
}

// Run the script
createDummyUsers().then(() => {
    console.log('✅ Script completed successfully');
    process.exit(0);
}).catch(error => {
    console.error('💥 Script failed:', error);
    process.exit(1);
});
