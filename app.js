// Main Application JavaScript for Whiteout Survival Dashboard

// Centralized state
let AppState = {
    heroes: [],
    events: [],
    tierData: {},
    users: { users: [], currentUser: null },
    editableTexts: {},
};

// --- API Functions ---
async function loadDataFromServer() {
    try {
        const response = await fetch('/api/data');
        if (!response.ok) throw new Error('Failed to fetch data');
        const data = await response.json();
        AppState = { ...AppState, ...data };
        console.log('🔥 Data loaded from server:', AppState);
    } catch (error) {
        console.error('Could not load data from server:', error);
        // Fallback to empty state
    }
}

async function saveDataToServer() {
    try {
        await fetch('/api/data', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(AppState),
        });
        console.log('🔥 Data saved to server');
    } catch (error) {
        console.error('Could not save data to server:', error);
    }
}

class WhiteoutDashboard {
    constructor() {
        this.containerNumbersVisible = false;
        this.currentPage = 'home';
        // The properties below are now managed by AppState
        // this.heroes = [];
        // this.events = [];
        // this.editableTexts = {};

        this.init();
    }

    async init() {
        await loadDataFromServer(); // Load all data at the start

        // Pass the relevant parts of the state to the managers
        this.authSystem = new AuthSystem(AppState.users);
        this.heroManager = new HeroManager(AppState.heroes, AppState.tierData);

        this.renderInitialUI();
        this.setupEventListeners();
        this.updateLoginStatus();
        this.loadEditableTexts(); // Now uses AppState
    }

    // ... (rest of the class remains largely the same, but now reads/writes to AppState)

    // Example of a change:
    toggleContainerNumbers() {
        this.containerNumbersVisible = !this.containerNumbersVisible;
        // ... (this method does not affect AppState, so it remains the same)
    }

    // Example of a change that will need modification in other files:
    // Any method that used to modify this.heroes or this.tierData
    // will now modify AppState.heroes or AppState.tierData and then call saveDataToServer().
}

// All other methods in WhiteoutDashboard should be checked to ensure they
// use the central AppState instead of their own properties.
// The actual saving will be triggered by the modules that change the data (HeroManager, AuthSystem).

document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new WhiteoutDashboard();
}); 