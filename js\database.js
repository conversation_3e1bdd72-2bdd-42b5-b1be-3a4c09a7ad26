// Database connection and operations for WhiteoutSurvival Dashboard
const mysql = require('mysql2/promise');
const crypto = require('crypto');

class DatabaseManager {
    constructor() {
        this.pool = mysql.createPool({
            host: 'localhost',
            user: 'root',
            password: 'PolitieDB2025SecurePassword',
            database: 'whiteout_dashboard',
            waitForConnections: true,
            connectionLimit: 10,
            queueLimit: 0,
            acquireTimeout: 60000,
            timeout: 60000
        });
        
        console.log('🔥 Database: Connection pool created');
    }

    // Initialize database with setup script
    async initialize() {
        try {
            const connection = await this.pool.getConnection();
            console.log('🔥 Database: Connected successfully');
            connection.release();
            return true;
        } catch (error) {
            console.error('❄️ Database: Connection failed:', error);
            return false;
        }
    }

    // User authentication methods
    async authenticateUser(username, password) {
        try {
            const [rows] = await this.pool.execute(
                'SELECT id, username, role, display_name, last_login FROM users WHERE username = ? AND password = ?',
                [username, password]
            );

            if (rows.length > 0) {
                const user = rows[0];
                
                // Update last login
                await this.pool.execute(
                    'UPDATE users SET last_login = NOW() WHERE id = ?',
                    [user.id]
                );

                console.log('🔥 Database: User authenticated:', user.username);
                return user;
            }
            
            console.log('❄️ Database: Authentication failed for:', username);
            return null;
        } catch (error) {
            console.error('❄️ Database: Authentication error:', error);
            return null;
        }
    }

    // Session management
    async createSession(userId) {
        try {
            const sessionId = crypto.randomBytes(64).toString('hex');
            const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

            await this.pool.execute(
                'INSERT INTO sessions (id, user_id, expires_at) VALUES (?, ?, ?)',
                [sessionId, userId, expiresAt]
            );

            console.log('🔥 Database: Session created for user:', userId);
            return sessionId;
        } catch (error) {
            console.error('❄️ Database: Session creation error:', error);
            return null;
        }
    }

    async validateSession(sessionId) {
        try {
            const [rows] = await this.pool.execute(
                `SELECT s.user_id, u.username, u.role, u.display_name 
                 FROM sessions s 
                 JOIN users u ON s.user_id = u.id 
                 WHERE s.id = ? AND s.expires_at > NOW()`,
                [sessionId]
            );

            if (rows.length > 0) {
                console.log('🔥 Database: Session validated for user:', rows[0].username);
                return rows[0];
            }
            
            console.log('❄️ Database: Invalid or expired session:', sessionId);
            return null;
        } catch (error) {
            console.error('❄️ Database: Session validation error:', error);
            return null;
        }
    }

    async destroySession(sessionId) {
        try {
            await this.pool.execute('DELETE FROM sessions WHERE id = ?', [sessionId]);
            console.log('🔥 Database: Session destroyed:', sessionId);
            return true;
        } catch (error) {
            console.error('❄️ Database: Session destruction error:', error);
            return false;
        }
    }

    // Clean expired sessions
    async cleanExpiredSessions() {
        try {
            const [result] = await this.pool.execute('DELETE FROM sessions WHERE expires_at < NOW()');
            console.log('🔥 Database: Cleaned', result.affectedRows, 'expired sessions');
            return result.affectedRows;
        } catch (error) {
            console.error('❄️ Database: Session cleanup error:', error);
            return 0;
        }
    }

    // Editable texts methods
    async getEditableTexts() {
        try {
            const [rows] = await this.pool.execute('SELECT field_key, field_value FROM editable_texts');
            const texts = {};
            rows.forEach(row => {
                texts[row.field_key] = row.field_value;
            });
            console.log('🔥 Database: Loaded', rows.length, 'editable texts');
            return texts;
        } catch (error) {
            console.error('❄️ Database: Error loading editable texts:', error);
            return {};
        }
    }

    async updateEditableText(fieldKey, fieldValue, userId) {
        try {
            await this.pool.execute(
                `INSERT INTO editable_texts (field_key, field_value, updated_by) 
                 VALUES (?, ?, ?) 
                 ON DUPLICATE KEY UPDATE 
                 field_value = VALUES(field_value), 
                 updated_by = VALUES(updated_by)`,
                [fieldKey, fieldValue, userId]
            );
            console.log('🔥 Database: Updated editable text:', fieldKey);
            return true;
        } catch (error) {
            console.error('❄️ Database: Error updating editable text:', error);
            return false;
        }
    }

    // User management methods
    async getAllUsers() {
        try {
            const [rows] = await this.pool.execute(
                'SELECT id, username, role, display_name, chief_id, battle_power, timezone, created_at, last_login FROM users ORDER BY created_at DESC'
            );
            console.log('🔥 Database: Loaded', rows.length, 'users');
            return rows;
        } catch (error) {
            console.error('❄️ Database: Error loading users:', error);
            return [];
        }
    }

    async createUser(userData) {
        try {
            const { username, password, role = 'pending', displayName, chiefId, battlePower, timezone } = userData;
            
            const [result] = await this.pool.execute(
                'INSERT INTO users (username, password, role, display_name, chief_id, battle_power, timezone) VALUES (?, ?, ?, ?, ?, ?, ?)',
                [username, password, role, displayName, chiefId, battlePower, timezone]
            );
            
            console.log('🔥 Database: User created:', username);
            return result.insertId;
        } catch (error) {
            console.error('❄️ Database: Error creating user:', error);
            return null;
        }
    }

    async updateUserRole(userId, newRole) {
        try {
            await this.pool.execute('UPDATE users SET role = ? WHERE id = ?', [newRole, userId]);
            console.log('🔥 Database: User role updated:', userId, newRole);
            return true;
        } catch (error) {
            console.error('❄️ Database: Error updating user role:', error);
            return false;
        }
    }

    // Close database connection
    async close() {
        try {
            await this.pool.end();
            console.log('🔥 Database: Connection pool closed');
        } catch (error) {
            console.error('❄️ Database: Error closing connection:', error);
        }
    }
}

module.exports = DatabaseManager;
