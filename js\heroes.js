// Hero Management System for Fire Nation Dashboard

class HeroManager {
    // The constructor now receives references to the global AppState parts
    constructor(heroesState, tierDataState) {
        this.heroes = heroesState; // Reference to AppState.heroes
        this.tierData = tierDataState; // Reference to AppState.tierData

        this.tiers = ['S+', 'S', 'A+', 'A', 'B+', 'B', 'C', 'Don\'t Use'];
        this.eventTypes = [
            { id: 'bear-captain', name: 'Bear - Captain', icon: 'paw', category: 'Bear' },
            { id: 'bear-joiner', name: '<PERSON> - <PERSON><PERSON>', icon: 'paw', category: 'Bear' },
            { id: 'pvp-att-captain', name: 'PvP Attacker - Captain', icon: 'bullseye', category: 'PvP Attacker' },
            { id: 'pvp-att-joiner', name: 'Pv<PERSON> Attacker - Joiner', icon: 'bullseye', category: 'PvP Attacker' },
            { id: 'pvp-def-captain', name: 'Pv<PERSON> Defender - Captain', icon: 'shield-alt', category: 'PvP Defender' },
            { id: 'pvp-def-joiner', name: 'PvP Defender - Joiner', icon: 'shield-alt', category: 'PvP Defender' }
        ];
        this.currentEventType = 'bear-captain'; // Default event type
        this.currentEditingSlot = null;

        this.renderTierSystem(); // Initial render
    }

    // Safe notification helper
    showNotification(message, type = 'info') {
        if (window.dashboard && window.dashboard.showNotification) {
            window.dashboard.showNotification(message, type);
        } else {
            console.log(`🔥 Heroes [${type.toUpperCase()}]:`, message);
        }
    }

    // Render the tier system
    renderTierSystem() {
        console.log('Rendering tier system for all users');
        this.renderEventTypeSelector(); // Always render for everyone
        this.renderTiers();
        this.updateEventDescription();
    }

    // Render event type selector
    renderEventTypeSelector() {
        const selector = document.getElementById('eventTypeSelector');
        if (!selector) {
            console.log('Event type selector not found!');
            return;
        }

        console.log('Rendering event type selector...');
        selector.innerHTML = '';

        // Group by category
        const categories = [...new Set(this.eventTypes.map(et => et.category))];
        console.log('Categories:', categories);

        categories.forEach(category => {
            const categoryDiv = document.createElement('div');
            categoryDiv.className = 'event-category';

            const categoryTitle = document.createElement('div');
            categoryTitle.className = 'event-category-title';
            categoryTitle.textContent = category;
            categoryDiv.appendChild(categoryTitle);

            const categoryEvents = this.eventTypes.filter(et => et.category === category);
            console.log(`Events for ${category}:`, categoryEvents);

            categoryEvents.forEach(eventType => {
                const button = document.createElement('button');
                button.className = `event-type-btn ${eventType.id === this.currentEventType ? 'active' : ''}`;
                button.innerHTML = `
                    <i class="fas fa-${eventType.icon}"></i>
                    ${eventType.name}
                `;

                button.addEventListener('click', () => {
                    this.switchEventType(eventType.id);
                });

                categoryDiv.appendChild(button);
            });

            selector.appendChild(categoryDiv);
        });

        console.log('Event type selector rendered with', selector.children.length, 'categories');
    }

    // Render tiers for current event type
    renderTiers() {
        const tierSystemContainer = document.getElementById('heroTierSystem');
        if (!tierSystemContainer) {
            console.log('Hero tier system container not found!');
            return;
        }

        console.log('Rendering tiers for event type:', this.currentEventType);
        tierSystemContainer.innerHTML = '';

        this.tiers.forEach(tier => {
            // Get all lineups for this tier
            const tierLineups = this.getTierLineups(tier);

            // Check if user can edit
            const canEdit = window.authSystem?.getCurrentUser()?.role === 'admin' ||
                           window.authSystem?.getCurrentUser()?.role === 'contributor';

            console.log(`Tier ${tier}: lineups=${tierLineups.length}, canEdit=${canEdit}`);

            // Show tiers with content for all users, empty tiers only for editors
            if (tierLineups.length === 0 && !canEdit) {
                console.log(`Skipping empty tier ${tier} for non-editor`);
                return; // Skip empty tiers for non-editors
            }

            // Create tier section
            const tierSection = document.createElement('div');
            tierSection.className = 'tier-section mb-6';

            // Tier header with add button
            const tierHeader = document.createElement('div');
            tierHeader.className = 'flex items-center justify-between mb-4';

            const tierLabel = document.createElement('div');
            tierLabel.className = `tier-label tier-${tier.toLowerCase().replace('+', '-plus').replace(' ', '-').replace('\'', '')}`;
            tierLabel.textContent = tier;

            tierHeader.appendChild(tierLabel);

            // Add lineup button (only for editors)
            if (canEdit) {
                const addLineupBtn = document.createElement('button');
                addLineupBtn.className = 'bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 px-3 py-1 rounded text-sm transition-all duration-300';
                addLineupBtn.innerHTML = '<i class="fas fa-plus mr-1"></i>Add Lineup';
                addLineupBtn.addEventListener('click', () => {
                    this.addNewLineup(tier);
                });
                tierHeader.appendChild(addLineupBtn);
            }

            tierSection.appendChild(tierHeader);

            // Lineups container
            const lineupsContainer = document.createElement('div');
            lineupsContainer.className = 'space-y-3';

            // Show existing lineups
            if (tierLineups.length > 0) {
                tierLineups.forEach((lineup, lineupIndex) => {
                    const lineupRow = this.createLineupRow(tier, lineup, lineupIndex);
                    lineupsContainer.appendChild(lineupRow);
                });
            } else if (canEdit) {
                // Show empty lineup for editors if no lineups exist
                const emptyLineup = this.createEmptyLineup(tier, 0);
                lineupsContainer.appendChild(emptyLineup);
            }

            tierSection.appendChild(lineupsContainer);
            tierSystemContainer.appendChild(tierSection);
        });

        console.log('Tier system rendered with', tierSystemContainer.children.length, 'tier sections');
        console.log('Tier system HTML:', tierSystemContainer.innerHTML.substring(0, 200) + '...');
    }

    // Get all lineups for a tier
    getTierLineups(tier) {
        const eventTierData = this.tierData[this.currentEventType] || {};
        return eventTierData[tier] || [];
    }

    // Create lineup row
    createLineupRow(tier, lineup, lineupIndex) {
        const lineupRow = document.createElement('div');
        lineupRow.className = 'tier-row flex items-center gap-4 p-3 bg-gray-800 bg-opacity-30 rounded-lg';

        // Lineup number
        const lineupNumber = document.createElement('div');
        lineupNumber.className = 'text-sm font-bold text-orange-300 min-w-[60px]';
        lineupNumber.textContent = `#${lineupIndex + 1}`;

        // Hero slots container
        const slotsContainer = document.createElement('div');
        slotsContainer.className = 'flex gap-4 flex-1';

        // Create 3 slots per lineup (Infantry, Lancer, Marksman)
        const types = ['infantry', 'lancer', 'marksman'];
        types.forEach((type, slotIndex) => {
            const slot = document.createElement('div');
            slot.className = 'hero-slot';
            slot.dataset.tier = tier;
            slot.dataset.type = type;
            slot.dataset.lineup = lineupIndex;
            slot.dataset.position = slotIndex;

            // Check if there's a hero assigned to this slot
            const assignedHero = lineup.find(hero => hero.type === type);

            if (assignedHero) {
                this.renderHeroInSlot(slot, assignedHero);
            } else {
                this.renderEmptySlot(slot, type);
            }

            // Add click listener
            slot.addEventListener('click', () => {
                this.handleSlotClick(slot);
            });

            slotsContainer.appendChild(slot);
        });

        lineupRow.appendChild(lineupNumber);
        lineupRow.appendChild(slotsContainer);

        // Delete lineup button (only for editors)
        const canEdit = window.authSystem?.getCurrentUser()?.role === 'admin' ||
                        window.authSystem?.getCurrentUser()?.role === 'contributor';

        if (canEdit) {
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'bg-red-600 hover:bg-red-700 px-2 py-1 rounded text-sm transition-colors';
            deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
            deleteBtn.addEventListener('click', () => {
                this.deleteLineup(tier, lineupIndex);
            });
            lineupRow.appendChild(deleteBtn);
        }

        return lineupRow;
    }

    // Create empty lineup
    createEmptyLineup(tier, lineupIndex) {
        return this.createLineupRow(tier, [], lineupIndex);
    }

    // Add new lineup
    addNewLineup(tier) {
        // Ensure event type data exists
        if (!this.tierData[this.currentEventType]) {
            this.tierData[this.currentEventType] = {};
        }
        if (!this.tierData[this.currentEventType][tier]) {
            this.tierData[this.currentEventType][tier] = [];
        }

        // Add empty lineup
        this.tierData[this.currentEventType][tier].push([]);
        // saveDataToServer(); // This will be handled by the global save function
        this.renderTiers();

        this.showNotification(`Added new lineup to ${tier}! 🔥`, 'success');
    }

    // Delete lineup
    deleteLineup(tier, lineupIndex) {
        if (!this.tierData[this.currentEventType] || !this.tierData[this.currentEventType][tier]) {
            return;
        }

        this.tierData[this.currentEventType][tier].splice(lineupIndex, 1);
        // saveDataToServer(); // This will be handled by the global save function
        this.renderTiers();

        this.showNotification(`Deleted lineup from ${tier}! 🔥`, 'success');
    }

    // Switch event type
    switchEventType(eventTypeId) {
        console.log('🔥 Heroes: Switching to event type:', eventTypeId);
        this.currentEventType = eventTypeId;
        this.renderTierSystem();

        this.showNotification(`Switched to ${this.getEventTypeName(eventTypeId)}! 🔥`, 'info');
    }

    // Get event type name
    getEventTypeName(eventTypeId) {
        const eventType = this.eventTypes.find(et => et.id === eventTypeId);
        return eventType ? eventType.name : 'Unknown Event';
    }

    // Update event description
    updateEventDescription() {
        const titleElement = document.getElementById('currentEventTitle');
        const descElement = document.getElementById('eventDescription');

        if (titleElement) {
            titleElement.textContent = `${this.getEventTypeName(this.currentEventType)} - Formation Guide`;
        }

        if (descElement) {
            const descriptions = {
                'bear-captain': 'Bear Captain: All 3 expedition skills from each hero are active (9 skills total). Choose heroes with complementary skill sets for maximum bear damage.',
                'bear-joiner': 'Bear Joiner: Only the first expedition skill of your first hero is shared with the rally. Choose your first hero carefully!',
                'pvp-att-captain': 'PvP Attacker Captain: All 3 skills per hero active. Focus on offensive skills, troop damage bonuses, and enemy debuffs.',
                'pvp-att-joiner': 'PvP Attacker Joiner: Only first skill of first hero shared. Choose a hero with strong offensive first skill for maximum impact.',
                'pvp-def-captain': 'PvP Defender Captain: All 3 skills per hero active. Prioritize defensive skills, damage reduction, and healing abilities.',
                'pvp-def-joiner': 'PvP Defender Joiner: Only first skill of first hero shared. Choose a hero with strong defensive first skill to support the rally.'
            };

            descElement.textContent = descriptions[this.currentEventType] || '';
        }
    }
    
    // Get hero in specific slot (updated for lineups)
    getHeroInSlot(tier, type, lineupIndex = 0) {
        const eventTierData = this.tierData[this.currentEventType] || {};
        const tierLineups = eventTierData[tier] || [];
        const lineup = tierLineups[lineupIndex] || [];
        return lineup.find(hero => hero.type === type);
    }
    
    // Render hero in slot
    renderHeroInSlot(slot, hero) {
        slot.classList.add('filled');
        slot.innerHTML = `
            <img src="${hero.image || '/images/heroes/placeholder.png'}" alt="${hero.name}" class="hero-image">
            <div class="hero-type-icon">
                <i class="${this.getTypeIcon(hero.type)}"></i>
            </div>
            <div class="hero-name">${hero.name}</div>
        `;
    }
    
    // Render empty slot
    renderEmptySlot(slot, type) {
        slot.classList.remove('filled');
        slot.innerHTML = `
            <i class="${this.getTypeIcon(type)} text-gray-400 text-2xl mb-2"></i>
            <span class="text-xs text-gray-400">${type.charAt(0).toUpperCase() + type.slice(1)}</span>
        `;
    }
    
    // Get type icon
    getTypeIcon(type) {
        const icons = {
            infantry: 'fas fa-shield-alt',    // FontAwesome schild voor infantry
            lancer: 'ra ra-sword',            // RPG-Awesome zwaard als fallback voor lancer
            marksman: 'ra ra-crossbow'        // RPG-Awesome kruisboog voor marksman
        };

        const iconClass = icons[type] || 'fas fa-user';
        console.log(`🔥 Heroes: Getting icon for ${type}: ${iconClass}`);
        return iconClass;
    }
    
    // Handle slot click
    handleSlotClick(slot) {
        // Only allow editing if user has permissions
        if (!window.authSystem || !window.authSystem.getCurrentUser()) {
            this.showNotification('Please login to edit heroes! 🔥', 'error');
            return;
        }
        
        const user = window.authSystem.getCurrentUser();
        if (user.role !== 'admin' && user.role !== 'contributor') {
            this.showNotification('You need admin or contributor permissions! 🔥', 'error');
            return;
        }
        
        this.currentEditingSlot = {
            tier: slot.dataset.tier,
            type: slot.dataset.type,
            position: slot.dataset.position
        };
        
        this.showHeroSelectionModal();
    }
    
    // Show hero management modal
    showHeroManagementModal() {
        const modal = document.getElementById('heroManagementModal');
        if (modal) {
            modal.classList.remove('hidden');
            this.loadExistingHeroes();
        }
    }
    
    // Hide hero management modal
    hideHeroManagementModal() {
        const modal = document.getElementById('heroManagementModal');
        if (modal) {
            modal.classList.add('hidden');
        }
    }
    
    // Show hero selection modal
    showHeroSelectionModal() {
        const modal = document.getElementById('heroSelectionModal');
        if (modal) {
            modal.classList.remove('hidden');
            this.loadHeroSelectionGrid();
        }
    }
    
    // Hide hero selection modal
    hideHeroSelectionModal() {
        const modal = document.getElementById('heroSelectionModal');
        if (modal) {
            modal.classList.add('hidden');
        }
        this.currentEditingSlot = null;
    }
    
    // Add new hero
    async addNewHero() {
        const name = document.getElementById('heroName').value.trim();
        const type = document.getElementById('heroType').value;
        const imageFile = document.getElementById('heroImage').files[0];

        // Get expedition skills
        const skillGroups = document.querySelectorAll('.skill-input-group');
        const expeditionSkills = [];

        skillGroups.forEach(group => {
            const skillName = group.querySelector('.skill-name').value.trim();
            const skillDesc = group.querySelector('.skill-desc').value.trim();

            if (skillName && skillDesc) {
                expeditionSkills.push({
                    name: skillName,
                    description: skillDesc
                });
            }
        });

        if (!name || !type) {
            this.showNotification('Please fill in hero name and type! 🔥', 'error');
            return;
        }

        if (expeditionSkills.length !== 3) {
            this.showNotification('Please fill in all 3 expedition skills! 🔥', 'error');
            return;
        }

        const newHero = {
            id: 'hero_' + Date.now(),
            name,
            type,
            expeditionSkills,
            image: null
        };

        // Handle image upload
        if (imageFile) {
            const reader = new FileReader();
            reader.onload = (e) => {
                newHero.image = e.target.result;
                this.heroes.push(newHero);
                // saveDataToServer(); // This will be handled by the global save function
                this.loadExistingHeroes();
                this.clearAddHeroForm();
                this.showNotification(`Hero ${name} added with 3 expedition skills! 🔥`, 'success');
            };
            reader.readAsDataURL(imageFile);
        } else {
            this.heroes.push(newHero);
            // saveDataToServer(); // This will be handled by the global save function
            this.loadExistingHeroes();
            this.clearAddHeroForm();
            this.showNotification(`Hero ${name} added with 3 expedition skills! 🔥`, 'success');
        }
    }
    
    // Clear add hero form
    clearAddHeroForm() {
        document.getElementById('heroName').value = '';
        document.getElementById('heroType').value = '';
        document.getElementById('heroImage').value = '';

        // Clear all skill inputs
        document.querySelectorAll('.skill-name, .skill-desc').forEach(input => {
            input.value = '';
        });
    }
    
    // Load existing heroes in management modal
    loadExistingHeroes() {
        const container = document.getElementById('existingHeroesList');
        if (!container) return;
        
        container.innerHTML = '';
        
        this.heroes.forEach(hero => {
            const heroCard = document.createElement('div');
            heroCard.className = 'hero-card rounded-lg p-3 text-center cursor-pointer';
            heroCard.innerHTML = `
                <div class="w-16 h-16 mx-auto mb-2 rounded-lg overflow-hidden bg-gray-700 flex items-center justify-center">
                    ${hero.image ?
                        `<img src="${hero.image}" alt="${hero.name}" class="w-full h-full object-cover">` :
                        `<i class="${this.getTypeIcon(hero.type)} text-gray-400 text-xl"></i>`
                    }
                </div>
                <h5 class="font-bold text-xs">${hero.name}</h5>
                <p class="text-xs text-gray-400">${hero.type}</p>
            `;
            
            heroCard.addEventListener('click', () => {
                this.editHero(hero);
            });
            
            container.appendChild(heroCard);
        });
    }
    
    // Edit hero
    editHero(hero) {
        this.currentEditingHero = hero;
        this.showEditHeroModal(hero);
    }

    // Show edit hero modal
    showEditHeroModal(hero) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4';
        modal.id = 'editHeroModal';
        modal.innerHTML = `
            <div class="fire-glass rounded-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-xl font-bold fire-nation-title">
                            <i class="fas fa-edit fire-icon mr-2"></i>
                            Edit Hero: ${hero.name}
                        </h3>
                        <button class="close-edit-modal text-gray-400 hover:text-white">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>

                    <form id="editHeroForm" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-orange-200 mb-2">Hero Name</label>
                            <input type="text" id="editHeroName" value="${hero.name}" required class="w-full px-3 py-2 bg-gray-800 border border-orange-400 rounded-lg text-white focus:outline-none focus:border-yellow-400">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-orange-200 mb-2">Hero Type</label>
                            <select id="editHeroType" required class="w-full px-3 py-2 bg-gray-800 border border-orange-400 rounded-lg text-white focus:outline-none focus:border-yellow-400">
                                <option value="infantry" ${hero.type === 'infantry' ? 'selected' : ''}>Infantry</option>
                                <option value="lancer" ${hero.type === 'lancer' ? 'selected' : ''}>Lancer</option>
                                <option value="marksman" ${hero.type === 'marksman' ? 'selected' : ''}>Marksman</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-orange-200 mb-2">Profile Image</label>
                            <input type="file" id="editHeroImage" accept="image/*" class="w-full px-3 py-2 bg-gray-800 border border-orange-400 rounded-lg text-white focus:outline-none focus:border-yellow-400">
                            ${hero.image ? '<div class="mt-2 text-sm text-green-200">Current image will be kept if no new image is selected</div>' : ''}
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-orange-200 mb-2">Expedition Skills (3 required)</label>
                            <div class="space-y-3">
                                ${hero.expeditionSkills.map((skill, index) => `
                                    <div class="skill-input-group grid grid-cols-1 md:grid-cols-2 gap-2">
                                        <input type="text" placeholder="Skill ${index + 1} Name" value="${skill.name}" class="skill-name bg-gray-800 border border-orange-400 rounded-lg text-white px-3 py-2 focus:outline-none focus:border-yellow-400">
                                        <input type="text" placeholder="Skill ${index + 1} Description" value="${skill.description}" class="skill-desc bg-gray-800 border border-orange-400 rounded-lg text-white px-3 py-2 focus:outline-none focus:border-yellow-400">
                                    </div>
                                `).join('')}
                            </div>
                        </div>

                        <div class="flex gap-3">
                            <button type="submit" class="flex-1 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 px-4 py-2 rounded-lg transition-all duration-300 font-medium">
                                <i class="fas fa-save mr-2"></i>Save Changes
                            </button>
                            <button type="button" id="deleteHeroBtn" class="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 px-4 py-2 rounded-lg transition-all duration-300 font-medium">
                                <i class="fas fa-trash mr-2"></i>Delete
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        // Add event listeners
        modal.querySelector('.close-edit-modal').addEventListener('click', () => {
            document.body.removeChild(modal);
            this.currentEditingHero = null;
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
                this.currentEditingHero = null;
            }
        });

        modal.querySelector('#editHeroForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleEditHero(modal);
        });

        modal.querySelector('#deleteHeroBtn').addEventListener('click', () => {
            this.confirmDeleteHero(hero, modal);
        });

        document.body.appendChild(modal);
    }

    // Handle edit hero form submission
    handleEditHero(modal) {
        const name = modal.querySelector('#editHeroName').value.trim();
        const type = modal.querySelector('#editHeroType').value;
        const imageFile = modal.querySelector('#editHeroImage').files[0];

        // Get expedition skills
        const skillGroups = modal.querySelectorAll('.skill-input-group');
        const expeditionSkills = [];

        skillGroups.forEach(group => {
            const skillName = group.querySelector('.skill-name').value.trim();
            const skillDesc = group.querySelector('.skill-desc').value.trim();

            if (skillName && skillDesc) {
                expeditionSkills.push({
                    name: skillName,
                    description: skillDesc
                });
            }
        });

        if (!name || !type) {
            this.showNotification('Please fill in hero name and type! 🔥', 'error');
            return;
        }

        if (expeditionSkills.length !== 3) {
            this.showNotification('Please fill in all 3 expedition skills! 🔥', 'error');
            return;
        }

        // Find and update the hero
        const heroIndex = this.heroes.findIndex(h => h.id === this.currentEditingHero.id);
        if (heroIndex === -1) {
            this.showNotification('Hero not found! 🔥', 'error');
            return;
        }

        // Update hero data
        this.heroes[heroIndex].name = name;
        this.heroes[heroIndex].type = type;
        this.heroes[heroIndex].expeditionSkills = expeditionSkills;

        // Handle image update
        if (imageFile) {
            const reader = new FileReader();
            reader.onload = (e) => {
                this.heroes[heroIndex].image = e.target.result;
                this.finishEditHero(modal, name);
            };
            reader.readAsDataURL(imageFile);
        } else {
            this.finishEditHero(modal, name);
        }
    }

    // Finish editing hero
    finishEditHero(modal, heroName) {
        // saveDataToServer(); // This will be handled by the global save function
        this.loadExistingHeroes();
        this.updateHeroInTiers();
        document.body.removeChild(modal);
        this.currentEditingHero = null;
        this.showNotification(`Hero ${heroName} updated! 🔥`, 'success');
    }

    // Update hero in all tier assignments
    updateHeroInTiers() {
        // Update hero references in tier data
        Object.keys(this.tierData).forEach(eventType => {
            Object.keys(this.tierData[eventType]).forEach(tier => {
                this.tierData[eventType][tier].forEach(lineup => {
                    lineup.forEach((hero, index) => {
                        const updatedHero = this.heroes.find(h => h.id === hero.id);
                        if (updatedHero) {
                            lineup[index] = updatedHero;
                        }
                    });
                });
            });
        });
        // saveDataToServer(); // This will be handled by the global save function
        this.renderTiers();
    }

    // Confirm delete hero
    confirmDeleteHero(hero, modal) {
        const confirmModal = document.createElement('div');
        confirmModal.className = 'fixed inset-0 bg-black bg-opacity-75 z-60 flex items-center justify-center p-4';
        confirmModal.innerHTML = `
            <div class="fire-glass rounded-xl max-w-sm w-full p-6">
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle text-red-400 text-4xl mb-4"></i>
                    <h3 class="text-xl font-bold mb-4">Delete Hero</h3>
                    <p class="text-gray-300 mb-6">Are you sure you want to delete <strong>${hero.name}</strong>? This will remove the hero from all tier lists.</p>
                    <div class="flex gap-3">
                        <button id="confirmDelete" class="flex-1 bg-red-600 hover:bg-red-700 px-4 py-2 rounded-lg transition-colors font-medium">
                            <i class="fas fa-trash mr-2"></i>Delete
                        </button>
                        <button id="cancelDelete" class="flex-1 bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded-lg transition-colors font-medium">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        `;

        confirmModal.querySelector('#confirmDelete').addEventListener('click', () => {
            this.deleteHero(hero);
            document.body.removeChild(confirmModal);
            document.body.removeChild(modal);
        });

        confirmModal.querySelector('#cancelDelete').addEventListener('click', () => {
            document.body.removeChild(confirmModal);
        });

        document.body.appendChild(confirmModal);
    }

    // Delete hero
    deleteHero(hero) {
        // Remove from heroes array
        this.heroes = this.heroes.filter(h => h.id !== hero.id);
        // saveDataToServer(); // This will be handled by the global save function

        // Remove from all tier assignments
        Object.keys(this.tierData).forEach(eventType => {
            Object.keys(this.tierData[eventType]).forEach(tier => {
                this.tierData[eventType][tier].forEach(lineup => {
                    for (let i = lineup.length - 1; i >= 0; i--) {
                        if (lineup[i].id === hero.id) {
                            lineup.splice(i, 1);
                        }
                    }
                });
            });
        });

        // saveDataToServer(); // This will be handled by the global save function
        this.loadExistingHeroes();
        this.renderTiers();
        this.currentEditingHero = null;

        this.showNotification(`Hero ${hero.name} deleted! 🔥`, 'success');
    }
    
    // Load hero selection grid
    loadHeroSelectionGrid() {
        const container = document.getElementById('heroSelectionGrid');
        if (!container || !this.currentEditingSlot) return;
        
        container.innerHTML = '';
        
        // Filter heroes by type if needed
        const requiredType = this.currentEditingSlot.type;
        const availableHeroes = this.heroes.filter(hero => hero.type === requiredType);
        
        // Add empty slot option
        const emptySlot = document.createElement('div');
        emptySlot.className = 'hero-slot cursor-pointer';
        emptySlot.innerHTML = `
            <i class="fas fa-times text-red-400 text-2xl"></i>
            <span class="text-xs text-red-400 mt-1">Remove</span>
        `;
        emptySlot.addEventListener('click', () => {
            this.assignHeroToSlot(null);
        });
        container.appendChild(emptySlot);
        
        // Add hero options
        availableHeroes.forEach(hero => {
            const heroOption = document.createElement('div');
            heroOption.className = 'hero-slot cursor-pointer';
            heroOption.innerHTML = `
                <div class="w-16 h-16 rounded-lg overflow-hidden bg-gray-700 flex items-center justify-content-center">
                    ${hero.image ?
                        `<img src="${hero.image}" alt="${hero.name}" class="w-full h-full object-cover">` :
                        `<i class="${this.getTypeIcon(hero.type)} text-gray-400 text-xl"></i>`
                    }
                </div>
                <span class="text-xs mt-1">${hero.name}</span>
            `;
            
            heroOption.addEventListener('click', () => {
                this.assignHeroToSlot(hero);
            });
            
            container.appendChild(heroOption);
        });
    }
    
    // Assign hero to slot (updated for lineups)
    assignHeroToSlot(hero) {
        if (!this.currentEditingSlot) return;

        const { tier, type, lineup } = this.currentEditingSlot;
        const lineupIndex = parseInt(lineup) || 0;

        // Ensure event type data exists in the state
        if (!this.tierData[this.currentEventType]) {
            this.tierData[this.currentEventType] = {};
        }
        if (!this.tierData[this.currentEventType][tier]) {
            this.tierData[this.currentEventType][tier] = [];
        }

        // Ensure lineup exists in the state
        while (this.tierData[this.currentEventType][tier].length <= lineupIndex) {
            this.tierData[this.currentEventType][tier].push([]);
        }

        const currentLineup = this.tierData[this.currentEventType][tier][lineupIndex];
        const filteredLineup = currentLineup.filter(h => h.type !== type);

        if (hero) {
            filteredLineup.push(hero);
        }

        this.tierData[this.currentEventType][tier][lineupIndex] = filteredLineup;

        // --- Crucial Change ---
        saveDataToServer(); // Save the entire updated state to the server

        this.renderTiers(); // Re-render the UI
        this.hideHeroSelectionModal();
    }
    
    // Filter hero selection
    filterHeroSelection() {
        // This will be implemented when needed
        this.loadHeroSelectionGrid();
    }
    
    // Toggle tier edit mode
    toggleTierEditMode() {
        window.dashboard.showNotification('Tier edit mode toggled! 🔥', 'info');
    }
}

// Initialize hero manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('🔥 Heroes: DOM loaded, initializing Hero Manager...');
    try {
        // Assuming AppState is globally available or passed as an argument
        // For now, we'll create a placeholder structure if not available
        const heroesState = []; // Placeholder for global heroes
        const tierDataState = {}; // Placeholder for global tierData

        window.heroManager = new HeroManager(heroesState, tierDataState);
        console.log('🔥 Heroes: Hero Manager initialized successfully');
    } catch (error) {
        console.error('🔥 Heroes: Error initializing Hero Manager:', error);
    }
});

// Fallback initialization for hero manager
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        // Assuming AppState is globally available or passed as an argument
        // For now, we'll create a placeholder structure if not available
        const heroesState = []; // Placeholder for global heroes
        const tierDataState = {}; // Placeholder for global tierData

        if (!window.heroManager) {
            console.log('🔥 Heroes: Fallback initialization...');
            try {
                window.heroManager = new HeroManager(heroesState, tierDataState);
                console.log('🔥 Heroes: Fallback Hero Manager initialized');
            } catch (error) {
                console.error('🔥 Heroes: Fallback error:', error);
            }
        }
    });
} else {
    console.log('🔥 Heroes: DOM already loaded, initializing immediately...');
    try {
        // Assuming AppState is globally available or passed as an argument
        // For now, we'll create a placeholder structure if not available
        const heroesState = []; // Placeholder for global heroes
        const tierDataState = {}; // Placeholder for global tierData

        window.heroManager = new HeroManager(heroesState, tierDataState);
        console.log('🔥 Heroes: Immediate Hero Manager initialized');
    } catch (error) {
        console.error('🔥 Heroes: Immediate error:', error);
    }
}
