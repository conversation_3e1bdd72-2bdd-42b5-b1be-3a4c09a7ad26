// Database setup script for WhiteoutSurvival Dashboard
const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

async function setupDatabase() {
    let connection;
    
    try {
        console.log('🔥 Connecting to MariaDB...');
        
        // First connect without database to create it
        connection = await mysql.createConnection({
            host: 'localhost',
            user: 'root',
            password: 'PolitieDB2025SecurePassword',
            multipleStatements: true
        });
        
        console.log('✅ Connected to MariaDB successfully');
        
        // Read and execute the setup script
        const setupScript = fs.readFileSync(path.join(__dirname, 'database-setup.sql'), 'utf8');
        
        console.log('🔥 Executing database setup script...');
        
        // Split script into individual statements
        const statements = setupScript.split(';').filter(stmt => stmt.trim().length > 0);
        
        for (const statement of statements) {
            if (statement.trim()) {
                try {
                    await connection.execute(statement.trim());
                    console.log('✅ Executed:', statement.substring(0, 50) + '...');
                } catch (error) {
                    if (error.code === 'ER_DB_CREATE_EXISTS' || error.code === 'ER_TABLE_EXISTS_ERROR') {
                        console.log('ℹ️  Already exists:', statement.substring(0, 50) + '...');
                    } else {
                        console.error('❌ Error executing statement:', statement.substring(0, 50) + '...');
                        console.error('Error:', error.message);
                    }
                }
            }
        }
        
        console.log('🔥 Database setup completed successfully!');
        
        // Test the connection with the new database
        await connection.changeUser({ database: 'whiteout_dashboard' });
        
        // Verify tables were created
        const [tables] = await connection.execute('SHOW TABLES');
        console.log('📋 Created tables:', tables.map(t => Object.values(t)[0]));
        
        // Test user authentication
        const [users] = await connection.execute('SELECT username, role, display_name FROM users');
        console.log('👥 Users in database:', users);
        
    } catch (error) {
        console.error('❌ Database setup failed:', error);
        process.exit(1);
    } finally {
        if (connection) {
            await connection.end();
            console.log('🔥 Database connection closed');
        }
    }
}

// Run the setup
setupDatabase().then(() => {
    console.log('🎉 Database setup complete! Ready to start server.');
    process.exit(0);
}).catch(error => {
    console.error('💥 Setup failed:', error);
    process.exit(1);
});
