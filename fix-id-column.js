// Fix id column to be AUTO_INCREMENT
const mysql = require('mysql2/promise');

async function fixIdColumn() {
    let connection;
    
    try {
        console.log('🔥 Connecting to database...');
        
        connection = await mysql.createConnection({
            host: 'localhost',
            user: 'root',
            password: 'PolitieDB2025SecurePassword',
            database: 'whiteout_dashboard'
        });
        
        console.log('✅ Connected to database');
        
        // Fix id column to be AUTO_INCREMENT
        console.log('🔥 Making id column AUTO_INCREMENT...');
        try {
            await connection.execute('ALTER TABLE heroes MODIFY COLUMN id INT AUTO_INCREMENT PRIMARY KEY');
            console.log('✅ Fixed id column');
        } catch (error) {
            console.log('ℹ️  ID column fix:', error.message);
        }
        
        console.log('✅ ID column fixed');
        
    } catch (error) {
        console.error('❌ Error fixing id column:', error);
        process.exit(1);
    } finally {
        if (connection) {
            await connection.end();
            console.log('🔥 Database connection closed');
        }
    }
}

// Run the fix
fixIdColumn().then(() => {
    console.log('🎉 ID column fix complete!');
    process.exit(0);
}).catch(error => {
    console.error('💥 Fix failed:', error);
    process.exit(1);
});
