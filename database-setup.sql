-- WhiteoutSurvival Dashboard Database Setup
-- Create database and tables for persistent data storage

CREATE DATABASE IF NOT EXISTS whiteout_dashboard;
USE whiteout_dashboard;

-- Users table for authentication
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('pending', 'alliance_member', 'alliance_organiser', 'moderator', 'admin') DEFAULT 'pending',
    display_name VA<PERSON><PERSON><PERSON>(100),
    chief_id VARCHAR(50),
    battle_power BIGINT,
    timezone VARCHAR(50) DEFAULT 'UTC+0',
    discord_id VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL
);

-- Sessions table for persistent login
CREATE TABLE IF NOT EXISTS sessions (
    id VARCHAR(128) PRIMARY KEY,
    user_id INT NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_expires (expires_at),
    INDEX idx_user (user_id)
);

-- Editable texts table for admin text editing
CREATE TABLE IF NOT EXISTS editable_texts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    field_key VARCHAR(100) UNIQUE NOT NULL,
    field_value TEXT NOT NULL,
    updated_by INT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Heroes table
CREATE TABLE IF NOT EXISTS heroes (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    type ENUM('infantry', 'lancer', 'marksman') NOT NULL,
    image_url TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Hero skills table
CREATE TABLE IF NOT EXISTS hero_skills (
    id INT AUTO_INCREMENT PRIMARY KEY,
    hero_id VARCHAR(50) NOT NULL,
    skill_name VARCHAR(100) NOT NULL,
    skill_description TEXT,
    skill_order INT DEFAULT 0,
    FOREIGN KEY (hero_id) REFERENCES heroes(id) ON DELETE CASCADE
);

-- Tier data table
CREATE TABLE IF NOT EXISTS tier_lineups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tier VARCHAR(10) NOT NULL,
    lineup_index INT NOT NULL,
    slot_index INT NOT NULL,
    hero_id VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (hero_id) REFERENCES heroes(id) ON DELETE SET NULL,
    UNIQUE KEY unique_slot (tier, lineup_index, slot_index)
);

-- Events table
CREATE TABLE IF NOT EXISTS events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    event_type VARCHAR(50),
    start_date TIMESTAMP,
    end_date TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Insert default admin user
INSERT INTO users (username, password, role, display_name) 
VALUES ('admin', 'fna2024', 'admin', 'FNA Admin')
ON DUPLICATE KEY UPDATE 
    password = VALUES(password),
    role = VALUES(role),
    display_name = VALUES(display_name);

-- Insert default editable texts
INSERT INTO editable_texts (field_key, field_value) VALUES
('welcome-title', 'Welcome to FNA - Fire Nation'),
('welcome-subtitle', '🔥 Dominating State 1661 with Fire & Ice ❄️'),
('welcome-quote', '"Fire is the element of power" - Avatar Universe'),
('heroes-title', 'Hero Tier System'),
('events-title', 'Current Events'),
('stats-title', 'Alliance Statistics')
ON DUPLICATE KEY UPDATE 
    field_value = VALUES(field_value);
