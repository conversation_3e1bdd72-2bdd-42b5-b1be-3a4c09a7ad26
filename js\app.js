// Main Application JavaScript for Whiteout Survival Dashboard

// Centralized state
let AppState = {
    heroes: [],
    events: [],
    tierData: {},
    users: { users: [], currentUser: null },
    editableTexts: {},
};

// WebSocket Connection
let ws;
function setupWebSocket() {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    ws = new WebSocket(`${protocol}//${host}`);

    ws.onmessage = (event) => {
        const message = JSON.parse(event.data);
        
        if (message.type === 'initial-data' || message.type === 'data-update') {
            // Update local state
            AppState = { ...AppState, ...message.data };
            
            // Update UI components
            if (window.heroManager) window.heroManager.renderTierSystem();
            if (window.dashboard) window.dashboard.updateLoginStatus();
        }
    };

    ws.onclose = () => {
        console.log('WebSocket connection closed. Retrying in 5 seconds...');
        setTimeout(setupWebSocket, 5000);
    };
}

// API Functions
async function loadDataFromServer() {
    try {
        const response = await fetch('/api/data');
        if (!response.ok) throw new Error('Failed to fetch data');
        const data = await response.json();
        AppState = { ...AppState, ...data };
        console.log('🔥 Data loaded from server:', AppState);
    } catch (error) {
        console.error('Could not load data from server:', error);
    }
}

async function saveDataToServer() {
    try {
        await fetch('/api/data', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(AppState)
        });
        
        // Broadcast changes via WebSocket
        if (ws && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({
                type: 'data-update',
                data: AppState
            }));
        }
        
        console.log('🔥 Data saved and broadcasted');
    } catch (error) {
        console.error('Could not save data to server:', error);
    }
}

class WhiteoutDashboard {
    constructor() {
        this.heroes = [];
        this.events = [];
        this.containerNumbersVisible = false;
        this.currentPage = 'home';
        this.editableTexts = {};

        this.init();
    }
    
    async init() {
        await loadDataFromServer();
        // setupWebSocket(); // Disabled to prevent connection errors

        console.log('🔥 App: AppState after loading:', AppState);
        console.log('🔥 App: AppState.users:', AppState.users);

        // Pass the correct users data to AuthSystem
        this.authSystem = new AuthSystem(AppState.users);
        window.authSystem = this.authSystem; // Make globally available

        this.heroManager = new HeroManager(AppState.heroes, AppState.tierData);
        window.heroManager = this.heroManager; // Make globally available

        this.renderInitialUI();
        this.setupEventListeners();
        this.loadEditableTexts(); // Enable text editing for admins
        this.loadFooterOnlineUsers(); // Load online users for everyone
        this.updateLoginStatus();
    }

    renderInitialUI() {
        console.log('🔥 Rendering initial UI...');
        // Initial UI rendering is handled by individual managers
        // This function exists to prevent errors
    }

    // Create animated fire & ice particles effect
    createFireIceEffect() {
        const particlesContainer = document.getElementById('particlesContainer');

        // Reduce particles on mobile for better performance
        const isMobile = window.innerWidth < 768;
        const snowflakeCount = isMobile ? 10 : 25;
        const fireEmberCount = isMobile ? 5 : 15;

        // Create snowflakes (ice particles)
        for (let i = 0; i < snowflakeCount; i++) {
            const snowflake = document.createElement('div');
            snowflake.className = 'snowflake';
            snowflake.innerHTML = '❄';
            snowflake.style.left = Math.random() * 100 + '%';
            snowflake.style.animationDuration = (Math.random() * 4 + 4) + 's';
            snowflake.style.animationDelay = Math.random() * 4 + 's';
            snowflake.style.fontSize = (Math.random() * 6 + 8) + 'px';
            particlesContainer.appendChild(snowflake);
        }

        // Create fire embers (only on desktop for performance)
        if (!isMobile) {
            for (let i = 0; i < fireEmberCount; i++) {
                const ember = document.createElement('div');
                ember.className = 'fire-ember';
                ember.style.left = Math.random() * 100 + '%';
                ember.style.animationDuration = (Math.random() * 6 + 5) + 's';
                ember.style.animationDelay = Math.random() * 5 + 's';
                ember.style.fontSize = (Math.random() * 4 + 6) + 'px';
                particlesContainer.appendChild(ember);
            }
        }
    }
    
    // Setup event listeners
    setupEventListeners() {
        // Mobile navigation
        document.querySelectorAll('.mobile-nav a, nav a').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(link.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });

        // Edit heroes button
        const editHeroesBtn = document.getElementById('editHeroesBtn');
        if (editHeroesBtn) {
            editHeroesBtn.addEventListener('click', () => {
                this.toggleHeroEditMode();
            });
        }

        // Create user button
        const createUserBtn = document.getElementById('createUserBtn');
        if (createUserBtn) {
            createUserBtn.addEventListener('click', () => {
                this.showCreateUserModal();
            });
        }

        // Container toggle button (new implementation)
        const toggleContainersBtn = document.getElementById('toggleContainersBtn');
        if (toggleContainersBtn) {
            toggleContainersBtn.addEventListener('click', () => {
                this.toggleContainerIDs();
            });
        }

        // Navigation links
        const navLinks = document.querySelectorAll('nav a[href^="#"]');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = link.getAttribute('href').substring(1);
                console.log('🔥 Navigating to:', page);
                this.showSection(page);
            });
        });

        // Profile navigation
        const profileNavLink = document.getElementById('profileNavLink');
        if (profileNavLink) {
            profileNavLink.addEventListener('click', (e) => {
                e.preventDefault();
                this.showProfilePage();
            });
        }
    }

    // Setup hamburger menu
    setupHamburgerMenu() {
        const hamburgerMenu = document.getElementById('hamburgerMenu');
        const sideMenu = document.getElementById('sideMenu');
        const menuOverlay = document.getElementById('menuOverlay');
        const menuHome = document.getElementById('menuHome');
        const menuAdmin = document.getElementById('menuAdmin');
        const menuModerator = document.getElementById('menuModerator');
        const menuToggleContainers = document.getElementById('menuToggleContainers');

        // Toggle menu
        if (hamburgerMenu) {
            hamburgerMenu.addEventListener('click', () => {
                this.toggleSideMenu();
            });
        }

        // Close menu when clicking overlay
        if (menuOverlay) {
            menuOverlay.addEventListener('click', () => {
                this.closeSideMenu();
            });
        }

        // Menu navigation
        if (menuHome) {
            menuHome.addEventListener('click', (e) => {
                e.preventDefault();
                this.navigateToPage('home');
            });
        }

        if (menuAdmin) {
            menuAdmin.addEventListener('click', (e) => {
                e.preventDefault();
                this.navigateToPage('admin');
            });
        }

        if (menuModerator) {
            menuModerator.addEventListener('click', (e) => {
                e.preventDefault();
                this.navigateToPage('moderator');
            });
        }

        // Container toggle in menu (old - will be removed)
        if (menuToggleContainers) {
            menuToggleContainers.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleContainerIDs();
            });
        }

        // Container toggle in header (new)
        const toggleContainersBtn = document.getElementById('toggleContainersBtn');
        if (toggleContainersBtn) {
            toggleContainersBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleContainerIDs();
            });
        }

        // Admin panel buttons
        const uploadHeroesBtn = document.getElementById('uploadHeroesBtn');
        const createEventBtn = document.getElementById('createEventBtn');
        const backupDataBtn = document.getElementById('backupDataBtn');

        if (uploadHeroesBtn) {
            uploadHeroesBtn.addEventListener('click', () => {
                this.openHeroUpload();
            });
        }

        if (createEventBtn) {
            createEventBtn.addEventListener('click', () => {
                this.openCreateEvent();
            });
        }

        if (backupDataBtn) {
            backupDataBtn.addEventListener('click', () => {
                this.backupData();
            });
        }
    }

    // Setup authentication listeners
    setupAuthListeners() {
        // Listen for auth changes
        window.addEventListener('authChanged', (e) => {
            const user = e.detail.user;
            this.onAuthChanged(user);
        });
    }

    // Handle authentication changes
    onAuthChanged(user) {
        console.log('Auth changed, user:', user);

        // Force update all admin elements
        this.updateAdminVisibility(user);

        // Always update menu visibility
        setTimeout(() => {
            this.updateMenuVisibility(user);
            this.updateAdminVisibility(user); // Double check
        }, 100);

        if (user) {
            // User logged in
            if (user.role === 'admin') {
                console.log('Setting up admin permissions');
                // Load admin data
                this.loadUsersList();
            }

            // Update hero edit permissions
            if (user.role === 'admin' || user.role === 'contributor') {
                this.initializeDragAndDrop();
            }
        } else {
            // User logged out
            this.hideAllAdminElements();
            this.hideContainerNumbers();
            this.navigateToPage('home');
        }
    }

    // Update admin visibility
    updateAdminVisibility(user) {
        const adminElements = [
            'adminNavLink',
            'mobileAdminLink',
            'manageHeroesBtn',
            'toggleContainersBtn',
            'editTiersBtn'
        ];

        adminElements.forEach(elementId => {
            const element = document.getElementById(elementId);
            if (element) {
                if (user && (user.role === 'admin' || user.role === 'moderator')) {
                    element.classList.remove('hidden');
                    // Special handling for mobile admin link
                    if (elementId === 'mobileAdminLink') {
                        element.classList.add('flex');
                        element.style.display = 'flex';
                    } else {
                        element.style.display = 'block';
                    }
                } else {
                    element.classList.add('hidden');
                    // Special handling for mobile admin link
                    if (elementId === 'mobileAdminLink') {
                        element.classList.remove('flex');
                    }
                    element.style.display = 'none';
                }
            }
        });
    }

    // Hide all admin elements
    hideAllAdminElements() {
        const adminElements = [
            'admin',
            'adminNavLink',
            'mobileAdminLink',
            'manageHeroesBtn',
            'toggleContainersBtn',
            'editTiersBtn'
        ];

        adminElements.forEach(elementId => {
            const element = document.getElementById(elementId);
            if (element) {
                element.classList.add('hidden');
                element.style.display = 'none';
            }
        });
    }

    // Update menu visibility based on user role
    updateMenuVisibility(user) {
        const adminItems = document.querySelectorAll('.admin-only');
        const moderatorItems = document.querySelectorAll('.moderator-only');

        if (user && user.role === 'admin') {
            adminItems.forEach(item => {
                item.style.display = 'block';
            });
            moderatorItems.forEach(item => {
                item.style.display = 'block';
            });
        } else if (user && user.role === 'contributor') {
            adminItems.forEach(item => {
                item.style.display = 'none';
            });
            moderatorItems.forEach(item => {
                item.style.display = 'block';
            });
        } else {
            adminItems.forEach(item => {
                item.style.display = 'none';
            });
            moderatorItems.forEach(item => {
                item.style.display = 'none';
            });
        }
    }
    
    // Setup smooth scrolling for navigation
    setupSmoothScrolling() {
        // Add smooth scrolling behavior
        document.documentElement.style.scrollBehavior = 'smooth';
    }

    // Fix gradient text issues
    fixGradientText() {
        // Test if background-clip: text is supported
        const testElement = document.createElement('div');
        testElement.style.background = 'linear-gradient(45deg, red, blue)';
        testElement.style.webkitBackgroundClip = 'text';
        testElement.style.backgroundClip = 'text';
        testElement.style.webkitTextFillColor = 'transparent';
        testElement.textContent = 'Test';
        testElement.style.position = 'absolute';
        testElement.style.left = '-9999px';
        document.body.appendChild(testElement);

        // Check if the text is actually transparent (gradient working)
        const computedStyle = window.getComputedStyle(testElement);
        const isGradientWorking = computedStyle.webkitTextFillColor === 'rgba(0, 0, 0, 0)' ||
                                 computedStyle.webkitTextFillColor === 'transparent';

        document.body.removeChild(testElement);

        // If gradient text doesn't work, use fallback styling
        if (!isGradientWorking) {
            console.log('🔥 Gradient text not supported, using fallback styling');
            const fireNationTitles = document.querySelectorAll('.fire-nation-title');
            fireNationTitles.forEach(title => {
                title.style.background = 'none';
                title.style.webkitTextFillColor = 'initial';
                title.style.color = '#f59e0b';
                title.style.textShadow = '0 0 10px rgba(245, 158, 11, 0.8), 0 0 20px rgba(220, 38, 38, 0.6)';
            });
        }
    }
    
    // Load initial data
    async loadInitialData() {
        try {
            await this.loadHeroes();
            await this.loadEvents();
            await this.loadStats();
        } catch (error) {
            console.error('Error loading initial data:', error);
            this.showNotification('Error loading data', 'error');
        }
    }
    
    // Load heroes data
    async loadHeroes() {
        // For now, use placeholder data
        this.heroes = [
            {
                id: 1,
                name: 'Frost Guardian',
                image: null,
                skills: ['Ice Shield', 'Frost Blast', 'Frozen Domain'],
                description: 'A powerful tank hero with ice-based defensive abilities.',
                rank: 1
            },
            {
                id: 2,
                name: 'Fire Warrior',
                image: null,
                skills: ['Flame Strike', 'Burning Rage', 'Inferno'],
                description: 'High damage dealer with fire-based attacks.',
                rank: 2
            }
        ];
        
        this.renderHeroes();
    }
    
    // Load events data
    async loadEvents() {
        // Placeholder events data
        this.events = [
            {
                id: 1,
                name: 'Alliance War',
                description: 'Prepare your best heroes for the upcoming alliance war!',
                timeRemaining: '2 days',
                type: 'war'
            },
            {
                id: 2,
                name: 'Hero Recruitment',
                description: 'Special recruitment event with increased rates!',
                timeRemaining: '3 days',
                type: 'recruitment'
            }
        ];
    }
    
    // Load stats data
    async loadStats() {
        // Placeholder stats - in real app, this would come from API
        const stats = {
            activePlayers: 1247,
            alliances: 89,
            totalPower: '156M',
            daysActive: 42
        };
        
        this.updateStatsDisplay(stats);
    }
    
    // Render heroes in the rankings section
    renderHeroes() {
        const heroRankings = document.getElementById('heroRankings');
        if (!heroRankings) return;
        
        // Clear existing content
        heroRankings.innerHTML = '';
        
        // Create hero slots (5 slots for ranking)
        for (let i = 1; i <= 5; i++) {
            const heroSlot = document.createElement('div');
            heroSlot.className = 'hero-card rounded-lg p-4 text-center';
            heroSlot.dataset.slot = i;
            
            const hero = this.heroes.find(h => h.rank === i);
            
            if (hero) {
                heroSlot.innerHTML = `
                    <div class="w-20 h-20 mx-auto mb-3 rounded-lg overflow-hidden bg-gray-700 flex items-center justify-center">
                        ${hero.image ? 
                            `<img src="${hero.image}" alt="${hero.name}" class="w-full h-full object-cover">` :
                            `<i class="fas fa-user text-gray-400 text-2xl"></i>`
                        }
                    </div>
                    <h4 class="font-bold text-sm">${hero.name}</h4>
                    <p class="text-xs text-gray-400 mt-1">Rank ${i}</p>
                `;
                
                // Add click listener for hero details
                heroSlot.addEventListener('click', () => {
                    this.showHeroDetails(hero);
                });
            } else {
                heroSlot.innerHTML = `
                    <div class="hero-placeholder mx-auto mb-3">
                        <i class="fas fa-user text-gray-400 text-2xl"></i>
                    </div>
                    <h4 class="font-bold text-sm">Hero Slot ${i}</h4>
                    <p class="text-xs text-gray-400 mt-1">Drag hero here</p>
                `;
            }
            
            heroRankings.appendChild(heroSlot);
        }
        
        // Initialize drag and drop if user has edit permissions
        if (this.userRole === 'admin' || this.userRole === 'contributor') {
            this.initializeDragAndDrop();
        }
    }
    
    // Initialize drag and drop functionality
    initializeDragAndDrop() {
        const heroRankings = document.getElementById('heroRankings');
        if (!heroRankings) return;

        new Sortable(heroRankings, {
            animation: 150,
            ghostClass: 'dragging',
            onEnd: (evt) => {
                this.updateHeroRanking(evt.oldIndex, evt.newIndex);
            }
        });
    }

    // Load users list for admin
    loadUsersList() {
        const usersList = document.getElementById('usersList');
        if (!usersList || !window.authSystem) return;

        const users = window.authSystem.users;
        usersList.innerHTML = '';

        Object.values(users).forEach(user => {
            const userCard = document.createElement('div');
            userCard.className = 'fire-glass p-3 rounded-lg flex justify-between items-center';
            userCard.innerHTML = `
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 rounded-full bg-gradient-to-r from-red-500 to-orange-500 flex items-center justify-center text-white font-bold text-sm">
                        ${user.displayName.charAt(0).toUpperCase()}
                    </div>
                    <div>
                        <div class="font-medium text-white">${user.displayName}</div>
                        <div class="text-sm text-gray-400">@${user.username} • ${user.role}</div>
                    </div>
                </div>
                <div class="flex space-x-2">
                    <span class="px-2 py-1 text-xs rounded-full ${
                        user.role === 'admin' ? 'bg-red-600' :
                        user.role === 'contributor' ? 'bg-orange-600' : 'bg-blue-600'
                    } text-white">
                        ${user.role.toUpperCase()}
                    </span>
                </div>
            `;
            usersList.appendChild(userCard);
        });
    }

    // Show create user modal
    showCreateUserModal() {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4';
        modal.innerHTML = `
            <div class="fire-glass rounded-xl max-w-md w-full p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold fire-nation-title">
                        <i class="fas fa-user-plus fire-icon mr-2"></i>
                        Create New User
                    </h3>
                    <button class="close-modal text-gray-400 hover:text-white">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <form id="createUserForm" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-orange-200 mb-2">Display Name</label>
                        <input type="text" id="newDisplayName" required
                               class="w-full px-3 py-2 bg-gray-800 border border-orange-400 rounded-lg text-white focus:outline-none focus:border-yellow-400">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-orange-200 mb-2">Username</label>
                        <input type="text" id="newUsername" required
                               class="w-full px-3 py-2 bg-gray-800 border border-orange-400 rounded-lg text-white focus:outline-none focus:border-yellow-400">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-orange-200 mb-2">Password</label>
                        <input type="password" id="newPassword" required
                               class="w-full px-3 py-2 bg-gray-800 border border-orange-400 rounded-lg text-white focus:outline-none focus:border-yellow-400">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-orange-200 mb-2">Role</label>
                        <select id="newRole" required
                                class="w-full px-3 py-2 bg-gray-800 border border-orange-400 rounded-lg text-white focus:outline-none focus:border-yellow-400">
                            <option value="viewer">Viewer - Can only view content</option>
                            <option value="contributor">Contributor - Can edit content</option>
                            <option value="admin">Admin - Full access</option>
                        </select>
                    </div>

                    <div id="createUserError" class="hidden text-red-400 text-sm"></div>

                    <button type="submit" class="w-full bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700 px-4 py-2 rounded-lg transition-all duration-300 font-medium">
                        <i class="fas fa-user-plus mr-2"></i>
                        Create User
                    </button>
                </form>
            </div>
        `;

        // Add event listeners
        modal.querySelector('.close-modal').addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });

        modal.querySelector('#createUserForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleCreateUser(modal);
        });

        document.body.appendChild(modal);

        // Focus on display name field
        setTimeout(() => {
            modal.querySelector('#newDisplayName').focus();
        }, 100);
    }

    // Handle create user form submission
    async handleCreateUser(modal) {
        const displayName = modal.querySelector('#newDisplayName').value;
        const username = modal.querySelector('#newUsername').value;
        const password = modal.querySelector('#newPassword').value;
        const role = modal.querySelector('#newRole').value;
        const errorDiv = modal.querySelector('#createUserError');

        // Clear previous errors
        errorDiv.classList.add('hidden');

        try {
            // Check if username already exists
            if (window.authSystem.users[username]) {
                errorDiv.textContent = 'Username already exists';
                errorDiv.classList.remove('hidden');
                return;
            }

            // Create user via API
            const response = await fetch('/api/users', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username,
                    password,
                    displayName,
                    role
                })
            });

            if (response.ok) {
                // Close modal
                document.body.removeChild(modal);

                // Refresh users list
                this.loadUsersList();

                // Show success message
                this.showNotification(`User ${displayName} created successfully! 🔥`, 'success');
            } else {
                const error = await response.json();
                errorDiv.textContent = error.error || 'Failed to create user';
                errorDiv.classList.remove('hidden');
            }

        } catch (error) {
            errorDiv.textContent = error.message;
            errorDiv.classList.remove('hidden');
        }
    }
    
    // Update hero ranking after drag and drop
    updateHeroRanking(oldIndex, newIndex) {
        // Update hero rankings logic
        console.log(`Moved hero from position ${oldIndex} to ${newIndex}`);
        // In real app, this would save to database
    }
    
    // Show hero details modal
    showHeroDetails(hero) {
        // Create and show modal with hero details
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4';
        modal.innerHTML = `
            <div class="frost-glass rounded-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl font-bold">${hero.name}</h3>
                        <button class="close-modal text-gray-400 hover:text-white">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    <div class="text-center mb-4">
                        <div class="w-24 h-24 mx-auto rounded-lg overflow-hidden bg-gray-700 flex items-center justify-center mb-3">
                            ${hero.image ? 
                                `<img src="${hero.image}" alt="${hero.name}" class="w-full h-full object-cover">` :
                                `<i class="fas fa-user text-gray-400 text-3xl"></i>`
                            }
                        </div>
                        <p class="text-gray-300">${hero.description}</p>
                    </div>
                    <div>
                        <h4 class="font-bold mb-2">Skills:</h4>
                        <ul class="space-y-1">
                            ${hero.skills.map(skill => `<li class="text-sm text-blue-300">• ${skill}</li>`).join('')}
                        </ul>
                    </div>
                </div>
            </div>
        `;
        
        // Add close functionality
        modal.querySelector('.close-modal').addEventListener('click', () => {
            document.body.removeChild(modal);
        });
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
        
        document.body.appendChild(modal);
    }
    
    // Update stats display
    updateStatsDisplay(stats) {
        // Update the stats section with real data
        const statsSection = document.querySelector('#stats .grid');
        if (statsSection) {
            statsSection.innerHTML = `
                <div class="text-center">
                    <div class="text-2xl md:text-3xl font-bold text-blue-300">${stats.activePlayers.toLocaleString()}</div>
                    <div class="text-sm text-blue-200">Active Players</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl md:text-3xl font-bold text-green-300">${stats.alliances}</div>
                    <div class="text-sm text-green-200">Alliances</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl md:text-3xl font-bold text-yellow-300">${stats.totalPower}</div>
                    <div class="text-sm text-yellow-200">Total Power</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl md:text-3xl font-bold text-red-300">${stats.daysActive}</div>
                    <div class="text-sm text-red-200">Days Active</div>
                </div>
            `;
        }
    }
    
    // Toggle hero edit mode
    toggleHeroEditMode() {
        // Toggle edit mode for heroes
        console.log('Toggle hero edit mode');
        this.showNotification('Hero edit mode toggled! 🔥', 'info');
    }

    // Toggle container IDs visibility (new implementation)
    toggleContainerIDs() {
        console.log('🔥 App: Toggling container IDs');
        this.containerNumbersVisible = !this.containerNumbersVisible;

        const containerNumbers = document.querySelectorAll('.container-number');
        const toggleBtn = document.getElementById('toggleContainersBtn');
        const toggleText = document.getElementById('toggleContainersText');

        console.log('🔥 App: Found', containerNumbers.length, 'container numbers');
        console.log('🔥 App: Toggle button:', !!toggleBtn);
        console.log('🔥 App: Toggle text:', !!toggleText);

        if (this.containerNumbersVisible) {
            // Show container numbers
            containerNumbers.forEach(number => {
                number.style.display = 'block';
                number.classList.add('visible');
            });
            if (toggleText) toggleText.textContent = 'Hide Container IDs';
            this.showNotification('Container IDs shown! 🔥', 'success');
        } else {
            // Hide container numbers
            containerNumbers.forEach(number => {
                number.style.display = 'none';
                number.classList.remove('visible');
            });
            if (toggleText) toggleText.textContent = 'Show Container IDs';
            this.showNotification('Container IDs hidden! ❄️', 'info');
        }
    }

    // Hide container numbers (used when logging out)
    hideContainerNumbers() {
        this.containerNumbersVisible = false;
        const containerNumbers = document.querySelectorAll('.container-number');

        containerNumbers.forEach(number => {
            number.classList.remove('visible');
        });

        this.updateMenuToggleText();
    }

    // Update menu toggle text (old)
    updateMenuToggleText() {
        const menuToggleText = document.getElementById('menuToggleText');
        if (menuToggleText) {
            menuToggleText.textContent = this.containerNumbersVisible ? 'Hide Container IDs' : 'Show Container IDs';
        }
    }

    // Update header toggle text (new)
    updateToggleContainersText() {
        const toggleContainersText = document.getElementById('toggleContainersText');
        if (toggleContainersText) {
            toggleContainersText.textContent = this.containerNumbersVisible ? 'Hide Container IDs' : 'Show Container IDs';
        }
    }

    // Show profile page
    showProfilePage() {
        console.log('🔥 Showing profile page');

        // Create profile modal
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4';
        modal.innerHTML = `
            <div class="fire-glass rounded-xl max-w-2xl w-full p-6 max-h-[90vh] overflow-y-auto">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold fire-nation-title">
                        <i class="fas fa-user fire-icon mr-2"></i>
                        User Profile
                    </h3>
                    <button class="close-profile-modal text-gray-400 hover:text-white">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <div class="space-y-6">
                    <!-- User Info -->
                    <div class="p-4 bg-gray-800 bg-opacity-50 rounded-lg">
                        <h4 class="font-bold mb-3 text-orange-200">Account Information</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-orange-200 mb-2">Display Name</label>
                                <input type="text" class="w-full px-3 py-2 bg-gray-800 border border-orange-400 rounded-lg text-white"
                                       placeholder="Your display name" value="${window.authSystem?.getCurrentUser()?.displayName || ''}">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-orange-200 mb-2">Chief ID</label>
                                <input type="text" class="w-full px-3 py-2 bg-gray-800 border border-orange-400 rounded-lg text-white"
                                       placeholder="Your Chief ID">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-orange-200 mb-2">Battle Power</label>
                                <input type="text" class="w-full px-3 py-2 bg-gray-800 border border-orange-400 rounded-lg text-white"
                                       placeholder="Your Battle Power">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-orange-200 mb-2">Timezone</label>
                                <select class="w-full px-3 py-2 bg-gray-800 border border-orange-400 rounded-lg text-white">
                                    <option>UTC+0 (GMT)</option>
                                    <option>UTC+1 (CET)</option>
                                    <option>UTC+2 (EET)</option>
                                    <option>UTC-5 (EST)</option>
                                    <option>UTC-8 (PST)</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Hero Gear (Placeholder) -->
                    <div class="p-4 bg-gray-800 bg-opacity-50 rounded-lg">
                        <h4 class="font-bold mb-3 text-orange-200">Hero Gear</h4>
                        <div class="text-center text-gray-400 py-8">
                            <i class="fas fa-hammer text-4xl mb-4"></i>
                            <p>Hero gear system coming soon!</p>
                            <p class="text-sm">Upload and manage your hero equipment</p>
                        </div>
                    </div>

                    <!-- Alt Accounts -->
                    <div class="p-4 bg-gray-800 bg-opacity-50 rounded-lg">
                        <h4 class="font-bold mb-3 text-orange-200">Alt Accounts</h4>
                        <div class="space-y-2">
                            <button class="w-full bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg transition-colors text-left">
                                <i class="fas fa-plus mr-2"></i>Add Alt Account
                            </button>
                        </div>
                    </div>

                    <!-- Save Button -->
                    <div class="flex gap-3">
                        <button class="flex-1 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 px-4 py-2 rounded-lg transition-all duration-300 font-medium">
                            <i class="fas fa-save mr-2"></i>Save Profile
                        </button>
                        <button class="close-profile-modal bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded-lg transition-colors font-medium">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Add event listeners
        modal.querySelectorAll('.close-profile-modal').forEach(btn => {
            btn.addEventListener('click', () => {
                document.body.removeChild(modal);
            });
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });

        document.body.appendChild(modal);
    }

    // Show specific section and hide others
    showSection(sectionId) {
        console.log('🔥 Showing section:', sectionId);

        // Hide all main sections
        const sections = document.querySelectorAll('main > section');
        sections.forEach(section => {
            section.classList.add('hidden');
        });

        // Show the requested section
        const targetSection = document.getElementById(sectionId);
        if (targetSection) {
            targetSection.classList.remove('hidden');
            console.log('🔥 Section shown:', sectionId);

            // Load section-specific data
            if (sectionId === 'admin') {
                this.loadUsersList();
            } else if (sectionId === 'heroes') {
                this.loadHeroesPage();
            } else if (sectionId === 'users') {
                this.loadUsersPage();
            }
        } else {
            console.log('❄️ Section not found:', sectionId);
            // Show home section as fallback
            const homeSection = document.querySelector('main > section:first-child');
            if (homeSection) {
                homeSection.classList.remove('hidden');
            }
        }
    }

    // Load users list for admin section
    async loadUsersList() {
        try {
            console.log('🔥 Loading users list...');
            const response = await fetch('/api/users');
            const users = await response.json();

            const usersList = document.getElementById('usersList');
            if (usersList && Array.isArray(users)) {
                usersList.innerHTML = users.map(user => `
                    <div class="fire-glass p-3 rounded-lg flex justify-between items-center">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                            <div>
                                <span class="font-bold text-${this.getRoleColor(user.role)}">${user.display_name || user.username}</span>
                                <span class="text-sm text-gray-400 ml-2">(@${user.username})</span>
                                ${user.role === 'pending' ? '<span class="text-xs text-yellow-400 ml-2">- Awaiting approval</span>' : ''}
                            </div>
                        </div>
                        <button class="change-role-btn text-orange-400 hover:text-yellow-300" data-user-id="${user.id}">
                            <i class="fas fa-edit"></i>
                        </button>
                    </div>
                `).join('');

                // Add role change listeners
                usersList.querySelectorAll('.change-role-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const userId = e.target.closest('.change-role-btn').dataset.userId;
                        this.showChangeRoleModal(userId);
                    });
                });
            }
        } catch (error) {
            console.error('❄️ Error loading users:', error);
        }
    }

    // Load heroes page with generation rows from database
    async loadHeroesPage() {
        console.log('🔥 Loading heroes page...');

        const heroesContainer = document.getElementById('heroesGenerations');
        if (!heroesContainer) return;

        try {
            // Load heroes from database
            const response = await fetch('/api/heroes');
            const heroes = await response.json();

            // Group heroes by generation
            const heroesByGeneration = {};
            heroes.forEach(hero => {
                if (!heroesByGeneration[hero.generation]) {
                    heroesByGeneration[hero.generation] = [];
                }
                heroesByGeneration[hero.generation].push(hero);
            });

            console.log('🔥 Loaded heroes by generation:', Object.keys(heroesByGeneration).map(gen => `Gen ${gen}: ${heroesByGeneration[gen].length} heroes`));

            // Render generations
            this.renderHeroesGenerations(heroesContainer, heroesByGeneration);
        } catch (error) {
            console.error('❄️ Error loading heroes:', error);
            // Fallback to empty data
            this.renderHeroesGenerations(heroesContainer, {});
        }
    }

    // Render heroes generations with proper tier system
    renderHeroesGenerations(container, heroesByGeneration) {
        // Define generation order and titles
        const generationOrder = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', 'rare', 'epic'];
        const generationTitles = {
            '1': 'Generation 1',
            '2': 'Generation 2',
            '3': 'Generation 3',
            '4': 'Generation 4',
            '5': 'Generation 5',
            '6': 'Generation 6',
            '7': 'Generation 7',
            '8': 'Generation 8',
            '9': 'Generation 9',
            '10': 'Generation 10',
            '11': 'Generation 11',
            'rare': 'Rare Heroes',
            'epic': 'Epic Heroes'
        };

        const generationColors = {
            '1': 'from-gray-600 to-gray-700',
            '2': 'from-gray-600 to-gray-700',
            '3': 'from-gray-600 to-gray-700',
            '4': 'from-gray-600 to-gray-700',
            '5': 'from-gray-600 to-gray-700',
            '6': 'from-gray-600 to-gray-700',
            '7': 'from-gray-600 to-gray-700',
            '8': 'from-gray-600 to-gray-700',
            '9': 'from-gray-600 to-gray-700',
            '10': 'from-gray-600 to-gray-700',
            '11': 'from-gray-600 to-gray-700',
            'rare': 'from-blue-600 to-blue-700',
            'epic': 'from-purple-600 to-purple-700'
        };

        container.innerHTML = generationOrder.filter(gen => heroesByGeneration[gen] && heroesByGeneration[gen].length > 0).map(generation => `
            <div class="generation-row mb-8" data-generation="${generation}">
                <h3 class="text-xl font-bold mb-4 fire-nation-title">
                    <i class="fas fa-star fire-icon mr-2"></i>
                    ${generationTitles[generation]} (${heroesByGeneration[generation].length} Heroes)
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    ${heroesByGeneration[generation].map(hero => `
                        <div class="hero-card fire-glass p-4 rounded-lg hover:scale-105 transition-transform border-2 ${this.getRarityBorder(hero.rarity)}">
                            <div class="flex items-center mb-3">
                                <div class="w-16 h-16 rounded-full overflow-hidden mr-4 border-2 ${this.getRarityBorder(hero.rarity)}">
                                    <img src="${hero.image_url || 'images/heroes/placeholder.png'}" alt="${hero.name}" class="w-full h-full object-cover"
                                         onerror="this.src='images/heroes/placeholder.png'">
                                </div>
                                <div>
                                    <h4 class="font-bold text-lg ${this.getRarityTextColor(hero.rarity)}">${hero.name}</h4>
                                    <div class="flex items-center">
                                        <i class="${this.getTypeIcon(hero.type)} text-orange-400 mr-2"></i>
                                        <span class="text-sm text-gray-300 capitalize">${hero.type}</span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        ${hero.tier_ranking ? `<span class="text-xs text-yellow-400">Tier ${hero.tier_ranking}</span>` : ''}
                                        <span class="text-xs px-2 py-1 rounded ${this.getRarityBadge(hero.rarity)}">${hero.rarity.toUpperCase()}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="skills-section mb-3">
                                <h5 class="font-semibold text-orange-200 mb-2">Skills:</h5>
                                <div class="flex flex-wrap gap-2">
                                    ${(hero.skills || []).map(skill => `
                                        <span class="skill-badge bg-gradient-to-r from-orange-600 to-red-600 px-2 py-1 rounded text-xs">
                                            ${skill}
                                        </span>
                                    `).join('')}
                                </div>
                            </div>
                            ${hero.stats ? `
                                <div class="stats-section">
                                    <h5 class="font-semibold text-orange-200 mb-2">Stats:</h5>
                                    <div class="grid grid-cols-3 gap-2 text-xs">
                                        <div class="text-center">
                                            <div class="text-red-400 font-bold">${hero.stats.attack || 0}</div>
                                            <div class="text-gray-400">ATK</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-blue-400 font-bold">${hero.stats.defense || 0}</div>
                                            <div class="text-gray-400">DEF</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-green-400 font-bold">${hero.stats.health || 0}</div>
                                            <div class="text-gray-400">HP</div>
                                        </div>
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                    `).join('')}
                </div>
            </div>
        `).join('');

        // Setup generation filters
        this.setupGenerationFilters();
    }

    // Get type icon for heroes
    getTypeIcon(type) {
        const icons = {
            'infantry': 'fas fa-shield-alt',
            'marksman': 'fas fa-crosshairs',
            'lancer': 'fas fa-sword'
        };
        return icons[type] || 'fas fa-user';
    }

    // Get rarity border color
    getRarityBorder(rarity) {
        const borders = {
            'common': 'border-gray-500',
            'rare': 'border-blue-400',
            'epic': 'border-purple-400',
            'legendary': 'border-yellow-400'
        };
        return borders[rarity] || 'border-gray-500';
    }

    // Get rarity text color
    getRarityTextColor(rarity) {
        const colors = {
            'common': 'text-gray-200',
            'rare': 'text-blue-300',
            'epic': 'text-purple-300',
            'legendary': 'text-yellow-300'
        };
        return colors[rarity] || 'text-gray-200';
    }

    // Get rarity badge style
    getRarityBadge(rarity) {
        const badges = {
            'common': 'bg-gray-600 text-gray-200',
            'rare': 'bg-blue-600 text-blue-200',
            'epic': 'bg-purple-600 text-purple-200',
            'legendary': 'bg-yellow-600 text-yellow-200'
        };
        return badges[rarity] || 'bg-gray-600 text-gray-200';
    }

    setupGenerationFilters() {
        const filters = document.querySelectorAll('.generation-filter');
        const rows = document.querySelectorAll('.generation-row');

        filters.forEach(filter => {
            filter.addEventListener('click', () => {
                // Update active filter
                filters.forEach(f => f.classList.remove('active'));
                filter.classList.add('active');

                const generation = filter.dataset.generation;

                // Show/hide rows
                rows.forEach(row => {
                    if (generation === 'all' || row.dataset.generation === generation) {
                        row.style.display = 'block';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });
        });
    }

    // Load footer online users and user list
    loadFooterUserManagement() {
        this.loadFooterOnlineUsers();
        this.loadFooterUsersList();
    }

    // Load online users in footer (mock data for now)
    loadFooterOnlineUsers() {
        const onlineUsersContainer = document.getElementById('footerOnlineUsers');
        if (!onlineUsersContainer) {
            console.log('❄️ Footer online users container not found');
            return;
        }

        console.log('🔥 Loading footer online users...');

        // Mock online users - in real app this would come from server
        const onlineUsers = [
            { username: 'admin', displayName: 'FNA Admin', role: 'admin' },
            { username: 'john_doe', displayName: 'John Doe', role: 'alliance_member' },
            { username: 'jane_smith', displayName: 'Jane Smith', role: 'alliance_organiser' }
        ];

        const userElements = onlineUsers.map(user =>
            `<span class="text-${this.getRoleColor(user.role)} font-medium mr-3">${user.displayName}</span>`
        ).join('');

        onlineUsersContainer.innerHTML = userElements;
        console.log('🔥 Footer online users loaded:', onlineUsers.length, 'users');
    }

    // Load footer users list
    async loadFooterUsersList() {
        try {
            console.log('🔥 Loading footer users list...');
            const response = await fetch('/api/users');
            const users = await response.json();

            const footerUsersList = document.getElementById('footerUsersList');
            if (footerUsersList && Array.isArray(users)) {
                footerUsersList.innerHTML = users.map(user => `
                    <div class="fire-glass p-2 rounded-lg flex justify-between items-center">
                        <div class="flex items-center space-x-2">
                            <div class="w-6 h-6 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white text-xs"></i>
                            </div>
                            <span class="text-${this.getRoleColor(user.role)} font-medium text-sm">${user.display_name || user.username}</span>
                            <span class="text-xs text-gray-400">(@${user.username})</span>
                            ${user.role === 'pending' ? '<span class="text-xs text-yellow-400">- Awaiting approval</span>' : ''}
                        </div>
                    </div>
                `).join('');
            }
        } catch (error) {
            console.error('❄️ Error loading footer users:', error);
        }
    }

    // Get role color for username display
    getRoleColor(role) {
        const colors = {
            'pending': 'yellow-400',
            'alliance_member': 'green-400',
            'alliance_organiser': 'blue-400',
            'moderator': 'purple-400',
            'admin': 'red-400'
        };
        return colors[role] || 'gray-400';
    }

    // Load users page with rank categories
    async loadUsersPage() {
        console.log('🔥 Loading users page...');

        try {
            const response = await fetch('/api/users');
            const users = await response.json();

            const usersByRankContainer = document.getElementById('usersByRank');
            if (!usersByRankContainer) return;

            // Group users by rank/role
            const usersByRank = {
                'admin': [],
                'moderator': [],
                'alliance_organiser': [],
                'alliance_member': [],
                'pending': []
            };

            users.forEach(user => {
                if (usersByRank[user.role]) {
                    usersByRank[user.role].push(user);
                }
            });

            // Render rank categories
            const rankTitles = {
                'admin': 'Administrators',
                'moderator': 'Moderators',
                'alliance_organiser': 'Alliance Organisers',
                'alliance_member': 'Alliance Members',
                'pending': 'Pending Approval'
            };

            const currentUser = window.authSystem?.getCurrentUser();
            const isAdmin = currentUser && currentUser.role === 'admin';

            usersByRankContainer.innerHTML = Object.keys(usersByRank).map(rank => {
                const rankUsers = usersByRank[rank];
                if (rankUsers.length === 0) return '';

                return `
                    <div class="rank-category mb-8">
                        <h3 class="text-xl font-bold mb-4 text-${this.getRoleColor(rank)}">
                            <i class="fas fa-star mr-2"></i>
                            ${rankTitles[rank]} (${rankUsers.length})
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            ${rankUsers.map(user => `
                                <div class="user-card fire-glass p-4 rounded-lg">
                                    <div class="flex items-center mb-3">
                                        <div class="w-12 h-12 bg-gradient-to-r from-${this.getRoleColor(rank).replace('-400', '-500')} to-${this.getRoleColor(rank).replace('-400', '-600')} rounded-full flex items-center justify-center mr-3">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                        <div>
                                            <h4 class="font-bold text-${this.getRoleColor(rank)}">${user.display_name || user.username}</h4>
                                            <p class="text-sm text-gray-400">@${user.username}</p>
                                            ${user.role === 'pending' ? '<p class="text-xs text-yellow-400">Awaiting approval</p>' : ''}
                                        </div>
                                    </div>
                                    ${isAdmin ? `
                                        <div class="admin-info text-xs text-gray-500 border-t border-gray-600 pt-2">
                                            <p>Joined: ${new Date(user.created_at).toLocaleDateString()}</p>
                                            <p>Last seen: ${this.getLastSeenText(user.last_login)}</p>
                                        </div>
                                    ` : ''}
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            }).filter(html => html).join('');

        } catch (error) {
            console.error('❄️ Error loading users page:', error);
        }
    }

    // Helper function for last seen text
    getLastSeenText(lastLogin) {
        if (!lastLogin) return 'Never';
        const date = new Date(lastLogin);
        const now = new Date();
        const diffMs = now - date;
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (diffDays === 0) return 'Today';
        if (diffDays === 1) return 'Yesterday';
        if (diffDays < 7) return `${diffDays} days ago`;
        return date.toLocaleDateString();
    }

    // Show change role modal
    showChangeRoleModal(userId) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4';
        modal.innerHTML = `
            <div class="fire-glass rounded-xl max-w-md w-full p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold fire-nation-title">
                        <i class="fas fa-user-edit fire-icon mr-2"></i>
                        Change User Role
                    </h3>
                    <button class="close-modal text-gray-400 hover:text-white">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <form id="changeRoleForm" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-orange-200 mb-2">New Role</label>
                        <select id="newRole" required class="w-full px-3 py-2 bg-gray-800 border border-orange-400 rounded-lg text-white focus:outline-none focus:border-yellow-400">
                            <option value="pending">Pending</option>
                            <option value="alliance_member">Alliance Member</option>
                            <option value="alliance_organiser">Alliance Organiser</option>
                            <option value="moderator">Moderator</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>

                    <button type="submit" class="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 px-4 py-2 rounded-lg transition-all duration-300 font-medium">
                        <i class="fas fa-save mr-2"></i>Update Role
                    </button>
                </form>
            </div>
        `;

        // Close modal handlers
        modal.querySelector('.close-modal').addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });

        // Form submit handler
        modal.querySelector('#changeRoleForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const newRole = modal.querySelector('#newRole').value;

            try {
                const response = await fetch(`/api/users/${userId}/role`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ role: newRole })
                });

                if (response.ok) {
                    this.showNotification('Role updated successfully! 🔥', 'success');
                    this.loadUsersList(); // Refresh user list
                    document.body.removeChild(modal);
                } else {
                    throw new Error('Failed to update role');
                }
            } catch (error) {
                console.error('❄️ Error updating role:', error);
                this.showNotification('Failed to update role! ❄️', 'error');
            }
        });

        document.body.appendChild(modal);
    }

    // Open hero upload functionality
    openHeroUpload() {
        if (window.heroManager) {
            window.heroManager.showHeroManagementModal();
        } else {
            this.showNotification('Hero manager not loaded! 🔥', 'error');
        }
    }

    // Open create event modal
    openCreateEvent() {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4';
        modal.innerHTML = `
            <div class="fire-glass rounded-xl max-w-md w-full p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold fire-nation-title">
                        <i class="fas fa-calendar-plus fire-icon mr-2"></i>
                        Create Event
                    </h3>
                    <button class="close-modal text-gray-400 hover:text-white">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <form id="createEventForm" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-orange-200 mb-2">Event Name</label>
                        <input type="text" id="eventName" required class="w-full px-3 py-2 bg-gray-800 border border-orange-400 rounded-lg text-white focus:outline-none focus:border-yellow-400">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-orange-200 mb-2">Event Type</label>
                        <select id="eventType" required class="w-full px-3 py-2 bg-gray-800 border border-orange-400 rounded-lg text-white focus:outline-none focus:border-yellow-400">
                            <option value="">Select Type</option>
                            <option value="alliance-war">Alliance War</option>
                            <option value="hero-recruitment">Hero Recruitment</option>
                            <option value="resource-gathering">Resource Gathering</option>
                            <option value="pvp-tournament">PvP Tournament</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-orange-200 mb-2">Description</label>
                        <textarea id="eventDescription" rows="3" class="w-full px-3 py-2 bg-gray-800 border border-orange-400 rounded-lg text-white focus:outline-none focus:border-yellow-400"></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-orange-200 mb-2">Duration</label>
                        <input type="text" id="eventDuration" placeholder="e.g., 3 days" class="w-full px-3 py-2 bg-gray-800 border border-orange-400 rounded-lg text-white focus:outline-none focus:border-yellow-400">
                    </div>

                    <button type="submit" class="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 px-4 py-2 rounded-lg transition-all duration-300 font-medium">
                        <i class="fas fa-plus mr-2"></i>Create Event
                    </button>
                </form>
            </div>
        `;

        // Add event listeners
        modal.querySelector('.close-modal').addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });

        modal.querySelector('#createEventForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleCreateEvent(modal);
        });

        document.body.appendChild(modal);
    }

    // Handle create event
    handleCreateEvent(modal) {
        const name = modal.querySelector('#eventName').value.trim();
        const type = modal.querySelector('#eventType').value;
        const description = modal.querySelector('#eventDescription').value.trim();
        const duration = modal.querySelector('#eventDuration').value.trim();

        if (!name || !type) {
            this.showNotification('Please fill in required fields! 🔥', 'error');
            return;
        }

        // Save event (simplified for now)
        const events = JSON.parse(localStorage.getItem('fna_events') || '[]');
        const newEvent = {
            id: 'event_' + Date.now(),
            name,
            type,
            description,
            duration,
            createdAt: new Date().toISOString(),
            createdBy: window.authSystem?.getCurrentUser()?.username || 'admin'
        };

        events.push(newEvent);
        localStorage.setItem('fna_events', JSON.stringify(events));

        document.body.removeChild(modal);
        this.showNotification(`Event "${name}" created! 🔥`, 'success');

        // Refresh events display if needed
        this.loadInitialData();
    }

    // Backup data functionality
    backupData() {
        const data = {
            heroes: JSON.parse(localStorage.getItem('fna_heroes') || '[]'),
            tierData: JSON.parse(localStorage.getItem('fna_tier_data') || '{}'),
            events: JSON.parse(localStorage.getItem('fna_events') || '[]'),
            users: JSON.parse(localStorage.getItem('fna_users') || '{}'),
            editableTexts: JSON.parse(localStorage.getItem('fna_editable_texts') || '{}'),
            exportDate: new Date().toISOString()
        };

        const dataStr = JSON.stringify(data, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `fire-nation-backup-${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        this.showNotification('Data backup downloaded! 🔥', 'success');
    }
    
    // Monitor performance and optimize accordingly
    monitorPerformance() {
        // Check if device seems to be struggling
        let frameCount = 0;
        let lastTime = performance.now();

        const checkFPS = () => {
            frameCount++;
            const currentTime = performance.now();

            if (currentTime - lastTime >= 1000) {
                const fps = frameCount;
                frameCount = 0;
                lastTime = currentTime;

                // If FPS is low on mobile, reduce effects
                if (fps < 30 && window.innerWidth < 768) {
                    this.enablePerformanceMode();
                }
            }

            requestAnimationFrame(checkFPS);
        };

        // Only monitor on mobile devices
        if (window.innerWidth < 768) {
            requestAnimationFrame(checkFPS);
        }
    }

    // Enable performance mode for low-end devices
    enablePerformanceMode() {
        console.log('🔥 Enabling performance mode for better experience');

        // Remove particles
        const particlesContainer = document.getElementById('particlesContainer');
        if (particlesContainer) {
            particlesContainer.innerHTML = '';
        }

        // Disable transitions temporarily
        const style = document.createElement('style');
        style.textContent = `
            .hero-card, .fire-glass {
                transition: none !important;
            }
        `;
        document.head.appendChild(style);

        // Remove after 5 seconds to allow normal interactions
        setTimeout(() => {
            document.head.removeChild(style);
        }, 5000);
    }

    // Toggle side menu
    toggleSideMenu() {
        const hamburgerMenu = document.getElementById('hamburgerMenu');
        const sideMenu = document.getElementById('sideMenu');
        const menuOverlay = document.getElementById('menuOverlay');

        if (hamburgerMenu && sideMenu && menuOverlay) {
            const isOpen = sideMenu.classList.contains('open');

            if (isOpen) {
                this.closeSideMenu();
            } else {
                hamburgerMenu.classList.add('open');
                sideMenu.classList.add('open');
                menuOverlay.classList.add('open');
            }
        }
    }

    // Close side menu
    closeSideMenu() {
        const hamburgerMenu = document.getElementById('hamburgerMenu');
        const sideMenu = document.getElementById('sideMenu');
        const menuOverlay = document.getElementById('menuOverlay');

        if (hamburgerMenu && sideMenu && menuOverlay) {
            hamburgerMenu.classList.remove('open');
            sideMenu.classList.remove('open');
            menuOverlay.classList.remove('open');
        }
    }

    // Navigate to different pages
    navigateToPage(page) {
        this.currentPage = page;
        this.closeSideMenu();

        // Update active menu item
        document.querySelectorAll('.side-menu-item').forEach(item => {
            item.classList.remove('active');
        });

        const activeMenuItem = document.getElementById(`menu${page.charAt(0).toUpperCase() + page.slice(1)}`);
        if (activeMenuItem) {
            activeMenuItem.classList.add('active');
        }

        // Show/hide content based on page
        if (page === 'home') {
            this.showHomePage();
        } else if (page === 'admin') {
            this.showAdminPage();
        } else if (page === 'moderator') {
            this.showModeratorPage();
        }

        this.showNotification(`Navigated to ${page.charAt(0).toUpperCase() + page.slice(1)} page! 🔥`, 'info');
    }

    // Show home page
    showHomePage() {
        // Show all home sections
        const sections = document.querySelectorAll('main > section');
        sections.forEach(section => {
            if (section.id !== 'admin') {
                section.style.display = 'block';
            } else {
                section.style.display = 'none';
            }
        });
    }

    // Show admin page
    showAdminPage() {
        // Hide home sections
        const sections = document.querySelectorAll('main > section');
        sections.forEach(section => {
            if (section.id === 'admin') {
                section.style.display = 'block';
                section.classList.remove('hidden');
            } else {
                section.style.display = 'none';
            }
        });
    }

    // Show moderator page (placeholder for future)
    showModeratorPage() {
        this.showNotification('Moderator page coming soon! 🛡️', 'info');
        // For now, just show home page
        this.showHomePage();
    }

    // Load and setup editable texts from database
    async loadEditableTexts() {
        try {
            console.log('🔥 Loading editable texts from database...');
            const response = await fetch('/api/texts');
            const texts = await response.json();

            this.editableTexts = texts;
            this.applyEditableTexts();

            console.log('🔥 Loaded editable texts from database:', Object.keys(texts).length, 'texts');
        } catch (error) {
            console.error('❄️ Error loading editable texts:', error);
            // Fallback to localStorage
            const saved = localStorage.getItem('fna_editable_texts');
            if (saved) {
                this.editableTexts = JSON.parse(saved);
                this.applyEditableTexts();
            }
        }

        // Setup edit functionality for admins
        this.setupEditableTexts();
    }

    // Setup editable text functionality
    setupEditableTexts() {
        const editableElements = document.querySelectorAll('.editable-text');
        editableElements.forEach(element => {
            // Add edit indicator
            const indicator = document.createElement('span');
            indicator.className = 'edit-indicator';
            indicator.textContent = 'EDIT';
            element.appendChild(indicator);

            // Add click listener for editing
            element.addEventListener('click', (e) => {
                const user = window.authSystem?.getCurrentUser();
                console.log('Edit clicked, user:', user);
                if (user && (user.role === 'admin' || user.role === 'contributor')) {
                    this.editText(element);
                } else {
                    console.log('No edit permission');
                }
            });
        });
    }

    // Edit text functionality
    editText(element) {
        const field = element.dataset.field;
        const currentText = element.textContent.replace('EDIT', '').trim();

        const input = document.createElement('input');
        input.type = 'text';
        input.value = currentText;
        input.className = 'bg-gray-800 text-white px-2 py-1 rounded border border-orange-400 focus:border-yellow-400 outline-none';
        input.style.width = '100%';

        element.innerHTML = '';
        element.appendChild(input);
        element.classList.add('editing');

        input.focus();
        input.select();

        const saveEdit = async () => {
            const newText = input.value.trim();
            if (newText) {
                try {
                    // Save to database
                    const response = await fetch('/api/texts', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            fieldKey: field,
                            fieldValue: newText
                        })
                    });

                    if (response.ok) {
                        this.editableTexts[field] = newText;
                        element.textContent = newText;
                        this.showNotification(`Updated ${field}! 🔥`, 'success');
                        console.log('🔥 Text saved to database:', field);
                    } else {
                        throw new Error('Failed to save to database');
                    }
                } catch (error) {
                    console.error('❄️ Error saving text:', error);
                    // Fallback to localStorage
                    this.editableTexts[field] = newText;
                    localStorage.setItem('fna_editable_texts', JSON.stringify(this.editableTexts));
                    element.textContent = newText;
                    this.showNotification(`Updated ${field} (local only)! ⚠️`, 'warning');
                }
            } else {
                element.textContent = currentText;
            }

            element.classList.remove('editing');
            // Re-add edit indicator
            const indicator = document.createElement('span');
            indicator.className = 'edit-indicator';
            indicator.textContent = 'EDIT';
            element.appendChild(indicator);
        };

        input.addEventListener('blur', saveEdit);
        input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                saveEdit();
            }
        });
    }

    // Apply saved editable texts
    applyEditableTexts() {
        Object.keys(this.editableTexts).forEach(field => {
            const element = document.querySelector(`[data-field="${field}"]`);
            if (element) {
                element.textContent = this.editableTexts[field];
            }
        });
    }

    // Show notification
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg ${
            type === 'error' ? 'bg-red-600' :
            type === 'success' ? 'bg-green-600' :
            'bg-blue-600'
        } text-white shadow-lg`;
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 3000);
    }
}

// Initialize the dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('🔥 DOM loaded, initializing dashboard...');
    window.dashboard = new WhiteoutDashboard();
});

// Fallback initialization if DOMContentLoaded doesn't fire
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        if (!window.dashboard) {
            console.log('🔥 Fallback: Initializing dashboard...');
            window.dashboard = new WhiteoutDashboard();
        }
    });
} else {
    // DOM is already loaded
    console.log('🔥 DOM already loaded, initializing dashboard immediately...');
    window.dashboard = new WhiteoutDashboard();
}
