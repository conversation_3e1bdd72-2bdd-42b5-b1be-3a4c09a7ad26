-- Heroes database setup for WhiteoutSurvival Dashboard

-- Drop existing heroes table if it exists
DROP TABLE IF EXISTS hero_uploads;
DROP TABLE IF EXISTS heroes;

-- Create heroes table
CREATE TABLE heroes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    generation INT NOT NULL,
    type ENUM('infantry', 'marksman', 'lancer') NOT NULL,
    image_url VARCHAR(255),
    description TEXT,
    skills JSON,
    stats JSON,
    tier_ranking INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_generation (generation),
    INDEX idx_type (type),
    INDEX idx_tier_ranking (tier_ranking)
);

-- Insert default heroes data
INSERT INTO heroes (name, generation, type, image_url, description, skills, stats, tier_ranking) VALUES
('Bahiti', 1, 'infantry', 'images/heroes/bahiti.png', 'Desert warrior with sand-based abilities', 
 JSO<PERSON>_ARRAY('Desert Storm', 'Sand Shield', 'Mirage Strike'), 
 JSON_OBJECT('attack', 850, 'defense', 920, 'health', 12500), 1),

('Flint', 1, 'marksman', 'images/heroes/flint.png', 'Expert marksman with precision abilities',
 JSON_ARRAY('Precision Shot', 'Eagle Eye', 'Explosive Arrow'),
 JSON_OBJECT('attack', 1200, 'defense', 650, 'health', 8500), 2),

('Natalia', 1, 'lancer', 'images/heroes/natalia.png', 'Cavalry commander with mounted combat skills',
 JSON_ARRAY('Cavalry Charge', 'Battle Fury', 'Lance Strike'),
 JSON_OBJECT('attack', 980, 'defense', 780, 'health', 10200), 3),

('Molly', 2, 'infantry', 'images/heroes/molly.png', 'Defensive specialist with shield abilities',
 JSON_ARRAY('Shield Wall', 'Defensive Stance', 'Counter Attack'),
 JSON_OBJECT('attack', 720, 'defense', 1100, 'health', 14000), 4),

('Patrick', 2, 'marksman', 'images/heroes/patrick.png', 'Stealth marksman with sniper abilities',
 JSON_ARRAY('Sniper Shot', 'Stealth', 'Critical Strike'),
 JSON_OBJECT('attack', 1350, 'defense', 580, 'health', 7800), 5),

('Wayne', 2, 'lancer', 'images/heroes/wayne.png', 'Mounted warrior with lance combat expertise',
 JSON_ARRAY('Lance Strike', 'Mounted Combat', 'Charge Attack'),
 JSON_OBJECT('attack', 1050, 'defense', 720, 'health', 9800), 6),

('Jessie', 3, 'infantry', 'images/heroes/jessie.png', 'Berserker with rage-based abilities',
 JSON_ARRAY('Berserker Rage', 'Iron Will', 'Fury Strike'),
 JSON_OBJECT('attack', 1100, 'defense', 850, 'health', 11500), 7),

('Zoe', 3, 'marksman', 'images/heroes/zoe.png', 'Multi-shot specialist with hunter abilities',
 JSON_ARRAY('Multi-Shot', 'Hunter\'s Mark', 'Piercing Arrow'),
 JSON_OBJECT('attack', 1280, 'defense', 620, 'health', 8200), 8),

('Gina', 3, 'lancer', 'images/heroes/gina.png', 'Combat master with spear expertise',
 JSON_ARRAY('Spear Thrust', 'Combat Mastery', 'Whirlwind Attack'),
 JSON_OBJECT('attack', 1150, 'defense', 800, 'health', 10500), 9);

-- Create hero_uploads table for tracking uploads
CREATE TABLE hero_uploads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    hero_id INT,
    original_filename VARCHAR(255),
    file_path VARCHAR(255),
    file_size INT,
    mime_type VARCHAR(100),
    uploaded_by INT,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (hero_id) REFERENCES heroes(id) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by) REFERENCES users(id)
);
