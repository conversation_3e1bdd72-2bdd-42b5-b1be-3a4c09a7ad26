// Fix heroes table by adding missing columns
const mysql = require('mysql2/promise');

async function fixHeroesTable() {
    let connection;
    
    try {
        console.log('🔥 Connecting to database...');
        
        connection = await mysql.createConnection({
            host: 'localhost',
            user: 'root',
            password: 'PolitieDB2025SecurePassword',
            database: 'whiteout_dashboard'
        });
        
        console.log('✅ Connected to database');
        
        // Check current table structure
        console.log('🔍 Checking current heroes table structure...');
        const [columns] = await connection.execute('DESCRIBE heroes');
        console.log('Current columns:', columns.map(col => col.Field));
        
        // Add missing columns if they don't exist
        const requiredColumns = [
            { name: 'generation', type: 'INT NOT NULL DEFAULT 1' },
            { name: 'type', type: "ENUM('infantry', 'marksman', 'lancer') NOT NULL DEFAULT 'infantry'" },
            { name: 'image_url', type: 'VARCHAR(255)' },
            { name: 'description', type: 'TEXT' },
            { name: 'skills', type: 'JSON' },
            { name: 'stats', type: 'JSON' },
            { name: 'tier_ranking', type: 'INT DEFAULT 0' },
            { name: 'created_by', type: 'INT' }
        ];
        
        const existingColumns = columns.map(col => col.Field);
        
        for (const column of requiredColumns) {
            if (!existingColumns.includes(column.name)) {
                try {
                    console.log(`🔥 Adding column: ${column.name}`);
                    await connection.execute(`ALTER TABLE heroes ADD COLUMN ${column.name} ${column.type}`);
                    console.log(`✅ Added column: ${column.name}`);
                } catch (error) {
                    console.error(`❌ Error adding column ${column.name}:`, error.message);
                }
            } else {
                console.log(`ℹ️  Column ${column.name} already exists`);
            }
        }
        
        // Fix id column to be AUTO_INCREMENT
        try {
            console.log('🔥 Making id column AUTO_INCREMENT...');
            await connection.execute('ALTER TABLE heroes MODIFY COLUMN id INT AUTO_INCREMENT PRIMARY KEY');
            console.log('✅ Fixed id column');
        } catch (error) {
            console.log('ℹ️  ID column already correct:', error.message);
        }

        // Add indexes
        try {
            await connection.execute('CREATE INDEX idx_generation ON heroes (generation)');
            console.log('✅ Added generation index');
        } catch (error) {
            console.log('ℹ️  Generation index already exists');
        }
        
        try {
            await connection.execute('CREATE INDEX idx_type ON heroes (type)');
            console.log('✅ Added type index');
        } catch (error) {
            console.log('ℹ️  Type index already exists');
        }
        
        // Clear existing data and insert heroes
        console.log('🔥 Clearing existing heroes data...');
        await connection.execute('DELETE FROM heroes');
        
        console.log('🔥 Inserting heroes data...');
        const heroesData = [
            [1, 'Bahiti', 1, 'infantry', 'images/heroes/bahiti.png', 'Desert warrior with sand-based abilities', JSON.stringify(['Desert Storm', 'Sand Shield', 'Mirage Strike']), JSON.stringify({attack: 850, defense: 920, health: 12500}), 1],
            [2, 'Flint', 1, 'marksman', 'images/heroes/flint.png', 'Expert marksman with precision abilities', JSON.stringify(['Precision Shot', 'Eagle Eye', 'Explosive Arrow']), JSON.stringify({attack: 1200, defense: 650, health: 8500}), 2],
            [3, 'Natalia', 1, 'lancer', 'images/heroes/natalia.png', 'Cavalry commander with mounted combat skills', JSON.stringify(['Cavalry Charge', 'Battle Fury', 'Lance Strike']), JSON.stringify({attack: 980, defense: 780, health: 10200}), 3],
            [4, 'Molly', 2, 'infantry', 'images/heroes/molly.png', 'Defensive specialist with shield abilities', JSON.stringify(['Shield Wall', 'Defensive Stance', 'Counter Attack']), JSON.stringify({attack: 720, defense: 1100, health: 14000}), 4],
            [5, 'Patrick', 2, 'marksman', 'images/heroes/patrick.png', 'Stealth marksman with sniper abilities', JSON.stringify(['Sniper Shot', 'Stealth', 'Critical Strike']), JSON.stringify({attack: 1350, defense: 580, health: 7800}), 5],
            [6, 'Wayne', 2, 'lancer', 'images/heroes/wayne.png', 'Mounted warrior with lance combat expertise', JSON.stringify(['Lance Strike', 'Mounted Combat', 'Charge Attack']), JSON.stringify({attack: 1050, defense: 720, health: 9800}), 6],
            [7, 'Jessie', 3, 'infantry', 'images/heroes/jessie.png', 'Berserker with rage-based abilities', JSON.stringify(['Berserker Rage', 'Iron Will', 'Fury Strike']), JSON.stringify({attack: 1100, defense: 850, health: 11500}), 7],
            [8, 'Zoe', 3, 'marksman', 'images/heroes/zoe.png', 'Multi-shot specialist with hunter abilities', JSON.stringify(['Multi-Shot', 'Hunter\'s Mark', 'Piercing Arrow']), JSON.stringify({attack: 1280, defense: 620, health: 8200}), 8],
            [9, 'Gina', 3, 'lancer', 'images/heroes/gina.png', 'Combat master with spear expertise', JSON.stringify(['Spear Thrust', 'Combat Mastery', 'Whirlwind Attack']), JSON.stringify({attack: 1150, defense: 800, health: 10500}), 9]
        ];
        
        for (const hero of heroesData) {
            await connection.execute(
                'INSERT INTO heroes (id, name, generation, type, image_url, description, skills, stats, tier_ranking) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
                hero
            );
        }
        
        console.log(`✅ Inserted ${heroesData.length} heroes`);
        
        // Verify the data
        const [heroes] = await connection.execute('SELECT name, generation, type FROM heroes ORDER BY generation, name');
        console.log('🦸 Heroes in database:', heroes.length);
        heroes.forEach(hero => {
            console.log(`  - ${hero.name} (Gen ${hero.generation}, ${hero.type})`);
        });
        
    } catch (error) {
        console.error('❌ Error fixing heroes table:', error);
        process.exit(1);
    } finally {
        if (connection) {
            await connection.end();
            console.log('🔥 Database connection closed');
        }
    }
}

// Run the fix
fixHeroesTable().then(() => {
    console.log('🎉 Heroes table fixed successfully!');
    process.exit(0);
}).catch(error => {
    console.error('💥 Fix failed:', error);
    process.exit(1);
});
